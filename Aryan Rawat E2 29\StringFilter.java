 import java.util.ArrayList;  

  

public class StringFilter {     

 public static void removeEvenLength(ArrayList<String> list) {         list.removeIf(str -> str.length() % 2 == 0);  

    }  

  

    public static void main(String[] args) {  

 ArrayList<String> words = new ArrayList<>();     

   words.add("hello");       

   words.add("world");     

     words.add("java");      

    words.add("code");     

     words.add("AI");          

words.add("Copilot");  

  

        removeEvenLength(words);  

        System.out.println("Filtered List: " + words);  

    }  

}  

  

  

  

  

  

  