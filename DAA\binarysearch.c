#include <stdio.h>
#include <stdlib.h>
int IndexofBinarySearch(int arr[],int size, int key)
{
     int low,mid,high;
     low = 0;
     high = size-1;
     while(low <= high)
     {
     mid = (low+high)/2;
     if(arr[mid] == key)
     return mid;
     if(arr[mid] > key)
     {
          high = mid-1;
     }
     else
     {
           low = mid+1;
     }
}
return -1;
}
int main()
{
     int arr[] = {5,10,15,20,25,30,35,40,45,50}; //sorted array
    int size = sizeof(arr)/sizeof(int);
     int key = 45; // Enter anything you want to find
     printf("The key is found at index %d." , IndexofBinarySearch(arr,size,key));
     return 0;
}