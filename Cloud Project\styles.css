/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --primary-color: #4a6fdc;
    --primary-dark: #3a5bb9;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 0.25rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

body {
    background-color: #f5f7fb;
    color: var(--gray-800);
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-outline {
    background-color: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    padding: 7px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.btn-outline:hover {
    background-color: var(--secondary-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Page Structure */
#app {
    min-height: 100vh;
    position: relative;
}

.page {
    display: none;
    min-height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.page.active {
    display: block;
}

.page-content {
    display: none;
    padding: 20px;
}

.page-content.active {
    display: block;
}

/* Login Page */
#login-page {
    background-color: #f5f7fb;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

.login-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    width: 100%;
    max-width: 450px;
    padding: 30px;
}

.logo {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.logo i {
    font-size: 3rem;
    margin-bottom: 10px;
}

/* Auth Tabs */
.auth-tabs {
    margin-top: 20px;
}

.tab-header {
    display: flex;
    border-bottom: 1px solid var(--gray-300);
    margin-bottom: 20px;
}

.tab {
    flex: 1;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-600);
    transition: var(--transition);
}

.tab.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 111, 220, 0.25);
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    width: auto;
    margin-right: 10px;
}

#login-form button,
#signup-form button {
    width: 100%;
    margin-top: 20px;
}

.forgot-password {
    text-align: center;
    margin-top: 15px;
}

.forgot-password a,
.terms a {
    color: var(--primary-color);
    text-decoration: none;
}

.forgot-password a:hover,
.terms a:hover {
    text-decoration: underline;
}

.terms {
    text-align: center;
    margin-top: 15px;
    font-size: 0.85rem;
    color: var(--gray-600);
}

/* Dashboard Page */
#dashboard-page {
    display: none;
}

#dashboard-page.active {
    display: flex;
}

.sidebar {
    width: 250px;
    background-color: white;
    background-image: linear-gradient(to bottom, #ffffff, #f8faff);
    height: 100vh;
    position: fixed;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    border-right: 1px solid rgba(74, 111, 220, 0.2);
}

.sidebar .logo {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
}

.sidebar .logo i {
    font-size: 2rem;
}

.sidebar .logo h2 {
    font-size: 1.5rem;
}

.nav-links {
    list-style: none;
    padding: 20px 0;
    flex-grow: 1;
}

.nav-links li {
    padding: 10px 20px;
    transition: var(--transition);
}

.nav-links li.active {
    background-color: var(--primary-color);
}

.nav-links li.active a {
    color: white;
}

.nav-links li:hover:not(.active) {
    background-color: var(--gray-100);
}

.nav-links a {
    color: var(--gray-700);
    text-decoration: none;
    display: flex;
    align-items: center;
}

.nav-links a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.logout {
    padding: 20px;
    border-top: 1px solid var(--gray-200);
}

.logout a {
    color: var(--gray-700);
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logout a i {
    margin-right: 10px;
}

.content {
    margin-left: 250px;
    width: calc(100% - 250px);
    min-height: 100vh;
    background-color: #f5f7fb;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path fill="%234a6fdc10" d="M50 0 L100 50 L50 100 L0 50 Z"/><path fill="%234a6fdc08" d="M30 30 L70 30 L70 70 L30 70 Z"/><path fill="%234a6fdc05" d="M45 15 L55 15 L55 85 L45 85 Z"/><path fill="%234a6fdc05" d="M15 45 L85 45 L85 55 L15 55 Z"/></svg>');
    background-size: 100px 100px;
    background-repeat: repeat;
    background-attachment: fixed;
}

header {
    background: linear-gradient(135deg, #ffffff 0%, #e6f0ff 100%);
    padding: 15px 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(74, 111, 220, 0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info span {
    margin-right: 15px;
    color: var(--gray-600);
}

.user-avatar i {
    font-size: 2rem;
    color: var(--gray-600);
}

/* Dashboard Content */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid rgba(74, 111, 220, 0.1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path fill="%234a6fdc05" d="M100 0 L100 100 L0 100 Z"/></svg>');
    background-size: cover;
    z-index: 0;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(74, 111, 220, 0.15);
    border-color: rgba(74, 111, 220, 0.3);
}

.card-icon {
    background: linear-gradient(135deg, rgba(74, 111, 220, 0.1) 0%, rgba(74, 111, 220, 0.2) 100%);
    color: var(--primary-color);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 15px;
    position: relative;
    z-index: 1;
    box-shadow: 0 3px 10px rgba(74, 111, 220, 0.1);
}

.card-info {
    position: relative;
    z-index: 1;
}

.card-info h3 {
    margin-bottom: 5px;
    color: var(--gray-800);
    font-weight: 600;
}

.card-info p {
    color: var(--gray-600);
    font-size: 0.9rem;
}

.upcoming-appointments {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(74, 111, 220, 0.1);
    position: relative;
    overflow: hidden;
}

.upcoming-appointments::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="150" height="150" viewBox="0 0 150 150"><path fill="%234a6fdc05" d="M150 0 C150 82.8 82.8 150 0 150 L0 0 Z"/><path fill="%234a6fdc03" d="M130 20 L130 130 L20 130 Z"/></svg>');
    background-size: cover;
    z-index: 0;
    opacity: 0.7;
}

.upcoming-appointments h2 {
    margin-bottom: 20px;
    color: var(--gray-800);
    position: relative;
    z-index: 1;
    font-weight: 600;
    border-bottom: 2px solid rgba(74, 111, 220, 0.1);
    padding-bottom: 10px;
}

.appointment-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    position: relative;
    z-index: 1;
}

.appointment-item {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    align-items: center;
}

.appointment-date {
    background-color: var(--primary-color);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.appointment-date .month {
    font-size: 0.8rem;
    text-transform: uppercase;
}

.appointment-date .day {
    font-size: 1.5rem;
    font-weight: bold;
}

.appointment-details {
    flex-grow: 1;
}

.appointment-details h3 {
    margin-bottom: 5px;
    color: var(--gray-800);
}

.appointment-details p {
    color: var(--gray-600);
    margin-bottom: 3px;
}

.appointment-actions {
    display: flex;
    gap: 10px;
}

.no-appointments {
    text-align: center;
    padding: 30px 20px;
    background-color: rgba(74, 111, 220, 0.03);
    border-radius: 10px;
    border: 1px dashed rgba(74, 111, 220, 0.2);
}

.no-appointments i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    opacity: 0.7;
}

.no-appointments h3 {
    margin-bottom: 10px;
    color: var(--gray-700);
}

.no-appointments p {
    color: var(--gray-600);
    margin-bottom: 20px;
}

.no-appointments .book-now-link {
    display: inline-block;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

/* Book Appointment */
.search-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.search-filters button {
    align-self: end;
}

.doctors-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.doctor-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.doctor-header {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--gray-200);
}

.doctor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.doctor-avatar i {
    font-size: 2rem;
    color: var(--gray-600);
}

.doctor-info h3 {
    margin-bottom: 5px;
}

.doctor-info p {
    color: var(--gray-600);
}

.doctor-rating {
    color: var(--warning-color);
    margin-top: 5px;
}

.doctor-details {
    padding: 20px;
}

.doctor-details p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.doctor-details p i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: var(--primary-color);
}

.doctor-slots {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.time-slot {
    background-color: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 5px 10px;
    cursor: pointer;
    transition: var(--transition);
}

.time-slot:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.time-slot.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.doctor-actions {
    padding: 15px 20px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
}

/* Maps */
.map-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    margin-bottom: 20px;
}

.map-filters {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.map {
    height: 400px;
    background-color: var(--gray-200);
    border-radius: 10px;
    box-shadow: var(--box-shadow);
}

.hospitals-container,
.pharmacies-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.hospital-card,
.pharmacy-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.hospital-card h3,
.pharmacy-card h3 {
    margin-bottom: 10px;
    color: var(--gray-800);
}

.hospital-card p,
.pharmacy-card p {
    margin-bottom: 5px;
    color: var(--gray-600);
    display: flex;
    align-items: center;
}

.hospital-card p i,
.pharmacy-card p i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: var(--primary-color);
}

.hospital-type {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-right: 5px;
    margin-bottom: 5px;
}

.hospital-type.general {
    background-color: var(--primary-color);
    color: white;
}

.hospital-type.emergency {
    background-color: var(--danger-color);
    color: white;
}

.hospital-type.maternity {
    background-color: var(--info-color);
    color: white;
}

.hospital-type.pediatric {
    background-color: var(--success-color);
    color: white;
}

.hospital-type.psychiatric {
    background-color: var(--warning-color);
    color: black;
}

/* Chatbot */
.chatbot-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    height: 500px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex-grow: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 10px;
}

.message.user {
    align-self: flex-end;
    background-color: var(--primary-color);
    color: white;
}

.message.bot {
    align-self: flex-start;
    background-color: var(--gray-200);
}

.chat-input {
    display: flex;
    padding: 10px;
    border-top: 1px solid var(--gray-200);
}

.chat-input input {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    outline: none;
}

.chat-input button {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Health Calculators */
.calculators-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.calculator-tabs .tab-header {
    margin-bottom: 20px;
}

.calculator {
    display: none;
}

.calculator.active {
    display: block;
}

.calculator h3 {
    margin-bottom: 20px;
    color: var(--gray-800);
}

.calculator-form {
    margin-bottom: 20px;
}

.calculator-result {
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-top: 20px;
}

.food-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.food-item {
    display: flex;
    align-items: center;
}

.food-checkbox {
    margin-right: 10px;
    width: auto;
}

.food-quantity {
    width: 60px;
    margin-left: 10px;
    padding: 5px;
}

/* Profile */
.profile-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.profile-avatar i {
    font-size: 3rem;
    color: var(--gray-600);
}

.profile-info h3 {
    margin-bottom: 5px;
}

.profile-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 30px;
}

.profile-section {
    margin-bottom: 30px;
}

.profile-section h3 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--gray-200);
}

.profile-field {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.profile-field label {
    width: 120px;
    color: var(--gray-700);
}

.profile-field input,
.profile-field select,
.profile-field textarea {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: var(--gray-100);
}

.profile-field input:disabled,
.profile-field select:disabled,
.profile-field textarea:disabled {
    background-color: var(--gray-100);
    color: var(--gray-800);
    cursor: not-allowed;
}

.edit-btn {
    background-color: transparent;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    margin-left: 10px;
}

.edit-btn:hover {
    color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 992px) {
    .map-container {
        grid-template-columns: 1fr;
    }

    .map {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }

    .sidebar .logo h2,
    .nav-links a span,
    .logout a span {
        display: none;
    }

    .content {
        margin-left: 70px;
        width: calc(100% - 70px);
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .appointment-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .appointment-date {
        margin-bottom: 15px;
    }

    .appointment-actions {
        margin-top: 15px;
        width: 100%;
        justify-content: space-between;
    }

    .profile-details {
        grid-template-columns: 1fr;
    }

    .profile-field {
        flex-direction: column;
        align-items: flex-start;
    }

    .profile-field label {
        width: 100%;
        margin-bottom: 5px;
    }

    .edit-btn {
        margin-left: 0;
        margin-top: 5px;
    }

    /* Blood availability responsive styles */
    .stock-grid {
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 6px;
    }

    .bank-footer {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .bank-actions {
        width: 100%;
        justify-content: center;
    }

    .bank-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Blood Availability Feature Styles */
.blood-bank-card {
    border-left: 4px solid var(--danger-color);
    transition: all 0.3s ease;
}

.blood-bank-card.has-selected-group {
    border-left-color: var(--success-color);
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.02) 0%, rgba(40, 167, 69, 0.05) 100%);
}

.bank-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.availability-badge {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3); }
    50% { box-shadow: 0 4px 16px rgba(40, 167, 69, 0.5); }
    100% { box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3); }
}

.bank-details {
    margin-bottom: 20px;
}

.bank-details p {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.bank-details i {
    margin-right: 8px;
    width: 16px;
    color: var(--primary-color);
}

.blood-stock {
    margin-bottom: 20px;
}

.blood-stock strong {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: var(--gray-800);
}

.blood-stock i {
    margin-right: 8px;
    color: var(--danger-color);
}

.stock-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
}

.stock-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    border-radius: 8px;
    border: 1px solid var(--gray-300);
    background: var(--gray-100);
    transition: all 0.3s ease;
}

.stock-item.available {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.2) 100%);
    border-color: var(--success-color);
    color: var(--success-color);
}

.stock-item.unavailable {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.2) 100%);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.stock-item.highlighted {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    border-width: 2px;
}

.blood-type {
    font-weight: 600;
    font-size: 0.9rem;
}

.units {
    font-size: 0.8rem;
    margin-top: 2px;
}

.no-stock {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: 20px;
}

.bank-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--gray-200);
}

.last-updated {
    font-size: 0.8rem;
    color: var(--gray-500);
    display: flex;
    align-items: center;
}

.last-updated i {
    margin-right: 5px;
}

.bank-actions {
    display: flex;
    gap: 10px;
}

.bank-actions button {
    padding: 6px 12px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.loading-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray-600);
}

.loading-message i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.error-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--danger-color);
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.1) 100%);
    border-radius: 8px;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.error-message i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-600);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--gray-400);
}

.no-results h3 {
    margin-bottom: 10px;
    color: var(--gray-700);
}

.results-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--gray-200);
}

.results-header h3 {
    color: var(--gray-800);
    font-weight: 600;
}

/* Enhanced form styling for blood availability */
#blood-availability-content .search-filters {
    background: linear-gradient(135deg, rgba(74, 111, 220, 0.05) 0%, rgba(74, 111, 220, 0.1) 100%);
    border: 1px solid rgba(74, 111, 220, 0.2);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
}

#blood-availability-content .form-group label {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8px;
}

#blood-availability-content .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    padding: 12px 24px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(74, 111, 220, 0.3);
    transition: all 0.3s ease;
}

#blood-availability-content .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 111, 220, 0.4);
}

#blood-availability-content .btn-primary:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: 0 2px 8px rgba(74, 111, 220, 0.2);
}
