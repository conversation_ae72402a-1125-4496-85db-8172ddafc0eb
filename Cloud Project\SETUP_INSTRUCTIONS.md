# Healthcare Portal Setup Instructions

## 1. Google Maps API Setup

### Step 1: Get Google Maps API Key
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - **Maps JavaScript API** (for maps and location services)
   - **Geocoding API** (for converting coordinates to addresses)
   - **Places API** (optional, for better location search)

### Step 2: Create API Credentials
1. Go to "Credentials" in the left sidebar
2. Click "Create Credentials" → "API Key"
3. Copy your API key
4. **Important**: Restrict your API key for security:
   - Click on your API key to edit it
   - Under "Application restrictions", select "HTTP referrers"
   - Add your domain (e.g., `localhost:8000/*` for local development)
   - Under "API restrictions", select "Restrict key" and choose the APIs you enabled

### Step 3: Add API Key to Your Project
1. Open `Cloud Project/index.html`
2. Find this line (around line 541):
   ```html
   <script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY_HERE&libraries=places&callback=initMap"></script>
   ```
3. Replace `YOUR_API_KEY_HERE` with your actual API key

### Step 4: Test Location Functionality
1. Start your local server: `python -m http.server 8000`
2. Open the application in your browser
3. Go to "Blood Availability" section
4. Click "Use My Current Location" button
5. Allow location access when prompted
6. The location should be detected and converted to a readable address

## 2. Local Database Management

### Database Structure
Your local database is organized in JSON files under the `database/` folder:

- `database/hospitals.json` - Hospital information
- `database/doctors.json` - Doctor profiles and schedules
- `database/pharmacies.json` - Pharmacy locations and medicine inventory
- `sample-blood-banks.json` - Blood bank data with stock levels

### Adding New Data

#### Adding a New Hospital
Edit `database/hospitals.json` and add a new hospital object:

```json
{
  "id": "hosp_006",
  "name": "Your Hospital Name",
  "address": "Full Address with City, State, PIN",
  "contact_number": "+91-XXX-XXXXXXX",
  "email": "<EMAIL>",
  "website": "https://hospital.com",
  "latitude": 30.XXXX,
  "longitude": 78.XXXX,
  "type": ["general", "emergency"],
  "specialties": ["Cardiology", "Orthopedics"],
  "facilities": ["24/7 Emergency", "ICU", "Blood Bank"],
  "rating": 4.2,
  "total_beds": 150,
  "available_beds": 25,
  "insurance_accepted": ["CGHS", "ESI", "Ayushman Bharat"],
  "visiting_hours": "10:00 AM - 8:00 PM",
  "emergency_contact": "+91-XXX-XXXXXXX"
}
```

#### Adding a New Doctor
Edit `database/doctors.json` and add a new doctor object:

```json
{
  "id": "doc_011",
  "name": "Dr. Your Name",
  "specialty": "general-physician",
  "qualification": "MBBS, MD",
  "experience": 10,
  "hospital_id": "hosp_001",
  "hospital_name": "Hospital Name",
  "contact_number": "+91-XXX-XXXXXXX",
  "email": "<EMAIL>",
  "consultation_fee": 500,
  "rating": 4.3,
  "total_reviews": 100,
  "available_days": ["Monday", "Tuesday", "Wednesday"],
  "available_times": ["09:00", "10:00", "11:00"],
  "languages": ["Hindi", "English"],
  "about": "Doctor's description and expertise"
}
```

#### Adding a New Pharmacy
Edit `database/pharmacies.json` and add to the pharmacies array:

```json
{
  "id": "pharm_006",
  "name": "Your Pharmacy Name",
  "address": "Full Address",
  "contact_number": "+91-XXX-XXXXXXX",
  "email": "<EMAIL>",
  "latitude": 30.XXXX,
  "longitude": 78.XXXX,
  "opening_hours": "08:00 AM - 10:00 PM",
  "services": ["Prescription Medicines", "OTC Medicines"],
  "payment_methods": ["Cash", "Card", "UPI"],
  "rating": 4.2,
  "home_delivery": true,
  "online_ordering": true
}
```

#### Adding a New Blood Bank
Edit `sample-blood-banks.json` and add to the blood_banks array:

```json
{
  "bank_name": "Your Blood Bank Name",
  "address": "Full Address",
  "contact_number": "+91-XXX-XXXXXXX",
  "email": "<EMAIL>",
  "latitude": 30.XXXX,
  "longitude": 78.XXXX,
  "blood_stock": {
    "A+": 25,
    "A-": 8,
    "B+": 18,
    "B-": 5,
    "O+": 32,
    "O-": 12,
    "AB+": 7,
    "AB-": 3
  },
  "last_updated": "2024-12-19T10:30:00Z"
}
```

### Getting Coordinates for Locations
To get latitude and longitude for new locations:

1. Go to [Google Maps](https://maps.google.com)
2. Search for the location
3. Right-click on the exact location
4. Click on the coordinates that appear
5. Copy the latitude and longitude values

### Specialty Codes for Doctors
Use these specialty codes in the doctor's `specialty` field:
- `general-physician`
- `cardiologist`
- `dermatologist`
- `orthopedic`
- `pediatrician`
- `gynecologist`
- `neurologist`
- `psychiatrist`
- `dentist`

### Hospital Types
Use these type codes in the hospital's `type` array:
- `general`
- `emergency`
- `maternity`
- `pediatric`
- `psychiatric`
- `super_specialty`
- `teaching`
- `research`

## 3. Testing Your Changes

1. After adding new data, refresh your browser
2. The database will automatically reload
3. Test the search functionality with your new data
4. Check the browser console for any errors

## 4. Backup and Version Control

1. Always backup your database files before making changes
2. Consider using Git to track changes to your database
3. Test thoroughly after adding new data

## 5. Troubleshooting

### Location Not Working
- Check if you've added the correct Google Maps API key
- Ensure the APIs are enabled in Google Cloud Console
- Check browser console for error messages
- Make sure you're accessing the site via HTTP/HTTPS (not file://)

### Database Not Loading
- Check browser console for errors
- Ensure JSON syntax is valid (use a JSON validator)
- Make sure file paths are correct
- Verify the local server is running

### Search Not Finding Results
- Check if the location names match what you're searching for
- Verify coordinates are correct
- Ensure specialty codes match exactly
- Check for typos in the data

## 6. Future Enhancements

You can extend the database by:
- Adding more detailed doctor schedules
- Including medicine inventory for pharmacies
- Adding patient reviews and ratings
- Implementing real-time bed availability
- Adding appointment booking confirmations
- Including insurance coverage details
