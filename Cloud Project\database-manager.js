// Local Database Manager for Healthcare Portal
class LocalDatabaseManager {
    constructor() {
        this.hospitals = [];
        this.doctors = [];
        this.pharmacies = [];
        this.bloodBanks = [];
        this.medicines = [];
        this.loaded = false;
    }

    // Load all database files
    async loadDatabase() {
        try {
            console.log('Loading local database...');
            
            // Load hospitals
            const hospitalsResponse = await fetch('./database/hospitals.json');
            const hospitalsData = await hospitalsResponse.json();
            this.hospitals = hospitalsData.hospitals || [];

            // Load doctors
            const doctorsResponse = await fetch('./database/doctors.json');
            const doctorsData = await doctorsResponse.json();
            this.doctors = doctorsData.doctors || [];

            // Load pharmacies
            const pharmaciesResponse = await fetch('./database/pharmacies.json');
            const pharmaciesData = await pharmaciesResponse.json();
            this.pharmacies = pharmaciesData.pharmacies || [];
            this.medicines = pharmaciesData.medicines || [];

            // Load blood banks
            const bloodBanksResponse = await fetch('./sample-blood-banks.json');
            const bloodBanksData = await bloodBanksResponse.json();
            this.bloodBanks = bloodBanksData.blood_banks || [];

            this.loaded = true;
            console.log('Local database loaded successfully');
            console.log(`Loaded: ${this.hospitals.length} hospitals, ${this.doctors.length} doctors, ${this.pharmacies.length} pharmacies, ${this.bloodBanks.length} blood banks`);
            
            return true;
        } catch (error) {
            console.error('Error loading local database:', error);
            return false;
        }
    }

    // Search hospitals by type and location
    searchHospitals(type = '', location = '', userLat = null, userLng = null) {
        if (!this.loaded) {
            console.warn('Database not loaded yet');
            return [];
        }

        let results = [...this.hospitals];

        // Filter by type
        if (type) {
            results = results.filter(hospital => 
                hospital.type.includes(type)
            );
        }

        // Filter by location (simple text search)
        if (location) {
            const locationLower = location.toLowerCase();
            results = results.filter(hospital =>
                hospital.address.toLowerCase().includes(locationLower) ||
                hospital.name.toLowerCase().includes(locationLower)
            );
        }

        // Calculate distances if user location provided
        if (userLat && userLng) {
            results = results.map(hospital => ({
                ...hospital,
                distance: this.calculateDistance(userLat, userLng, hospital.latitude, hospital.longitude)
            })).sort((a, b) => a.distance - b.distance);
        }

        return results;
    }

    // Search doctors by specialty and location
    searchDoctors(specialty = '', location = '', date = '', userLat = null, userLng = null) {
        if (!this.loaded) {
            console.warn('Database not loaded yet');
            return [];
        }

        let results = [...this.doctors];

        // Filter by specialty
        if (specialty) {
            results = results.filter(doctor => 
                doctor.specialty === specialty
            );
        }

        // Filter by location (search in hospital address)
        if (location) {
            const locationLower = location.toLowerCase();
            results = results.filter(doctor => {
                const hospital = this.hospitals.find(h => h.id === doctor.hospital_id);
                return hospital && (
                    hospital.address.toLowerCase().includes(locationLower) ||
                    hospital.name.toLowerCase().includes(locationLower)
                );
            });
        }

        // Add hospital information and calculate distances
        results = results.map(doctor => {
            const hospital = this.hospitals.find(h => h.id === doctor.hospital_id);
            let distance = null;
            
            if (userLat && userLng && hospital) {
                distance = this.calculateDistance(userLat, userLng, hospital.latitude, hospital.longitude);
            }
            
            return {
                ...doctor,
                hospital_address: hospital ? hospital.address : '',
                hospital_latitude: hospital ? hospital.latitude : null,
                hospital_longitude: hospital ? hospital.longitude : null,
                distance: distance
            };
        });

        // Sort by distance if available
        if (userLat && userLng) {
            results.sort((a, b) => (a.distance || Infinity) - (b.distance || Infinity));
        }

        return results;
    }

    // Search pharmacies by location
    searchPharmacies(location = '', medicine = '', userLat = null, userLng = null) {
        if (!this.loaded) {
            console.warn('Database not loaded yet');
            return [];
        }

        let results = [...this.pharmacies];

        // Filter by location
        if (location) {
            const locationLower = location.toLowerCase();
            results = results.filter(pharmacy =>
                pharmacy.address.toLowerCase().includes(locationLower) ||
                pharmacy.name.toLowerCase().includes(locationLower)
            );
        }

        // Calculate distances if user location provided
        if (userLat && userLng) {
            results = results.map(pharmacy => ({
                ...pharmacy,
                distance: this.calculateDistance(userLat, userLng, pharmacy.latitude, pharmacy.longitude)
            })).sort((a, b) => a.distance - b.distance);
        }

        return results;
    }

    // Search blood banks
    searchBloodBanks(bloodGroup = '', location = '', userLat = null, userLng = null) {
        if (!this.loaded) {
            console.warn('Database not loaded yet');
            return [];
        }

        let results = [...this.bloodBanks];

        // Filter by blood group availability
        if (bloodGroup) {
            results = results.filter(bank => 
                bank.blood_stock && bank.blood_stock[bloodGroup] > 0
            );
        }

        // Filter by location
        if (location) {
            const locationLower = location.toLowerCase();
            results = results.filter(bank =>
                bank.address.toLowerCase().includes(locationLower) ||
                bank.bank_name.toLowerCase().includes(locationLower)
            );
        }

        // Calculate distances if user location provided
        if (userLat && userLng) {
            results = results.map(bank => ({
                ...bank,
                distance: this.calculateDistance(userLat, userLng, bank.latitude, bank.longitude)
            })).sort((a, b) => a.distance - b.distance);
        }

        return results;
    }

    // Search medicines
    searchMedicines(query = '') {
        if (!this.loaded) {
            console.warn('Database not loaded yet');
            return [];
        }

        if (!query) return this.medicines;

        const queryLower = query.toLowerCase();
        return this.medicines.filter(medicine =>
            medicine.name.toLowerCase().includes(queryLower) ||
            medicine.generic_name.toLowerCase().includes(queryLower) ||
            medicine.brand_names.some(brand => brand.toLowerCase().includes(queryLower)) ||
            medicine.common_uses.some(use => use.toLowerCase().includes(queryLower))
        );
    }

    // Get doctor by ID
    getDoctorById(id) {
        return this.doctors.find(doctor => doctor.id === id);
    }

    // Get hospital by ID
    getHospitalById(id) {
        return this.hospitals.find(hospital => hospital.id === id);
    }

    // Calculate distance using Haversine formula
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Earth's radius in kilometers
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = 
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    // Get statistics
    getStats() {
        return {
            hospitals: this.hospitals.length,
            doctors: this.doctors.length,
            pharmacies: this.pharmacies.length,
            bloodBanks: this.bloodBanks.length,
            medicines: this.medicines.length,
            loaded: this.loaded
        };
    }
}

// Create global instance
window.localDB = new LocalDatabaseManager();

// Auto-load database when script loads
document.addEventListener('DOMContentLoaded', async function() {
    await window.localDB.loadDatabase();
    console.log('Database stats:', window.localDB.getStats());
});
