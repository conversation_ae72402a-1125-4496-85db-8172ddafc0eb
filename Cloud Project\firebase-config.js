// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getAnalytics } from "firebase/analytics";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBRDjU2ralmIe64sBXJQDE7mfJ3isuEKK0",
  authDomain: "healthcare-website-33881.firebaseapp.com",
  projectId: "healthcare-website-33881",
  storageBucket: "healthcare-website-33881.firebasestorage.app",
  messagingSenderId: "283412304605",
  appId: "1:283412304605:web:895712be148e5ce5fd0134",
  measurementId: "G-SQ27H6WW6L"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);
const analytics = getAnalytics(app);

// Export the Firebase services
export { auth, db, storage, analytics };
