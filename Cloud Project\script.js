
document.addEventListener('DOMContentLoaded', function() {
    // Firebase services are available as global variables
    try {
        // Check if Firebase is initialized
        if (window.firebaseAuth) {
            console.log('Firebase initialized successfully');
        } else {
            console.error('Firebase not initialized. Make sure Firebase SDK is loaded correctly.');
        }
    } catch (error) {
        console.error('Error accessing Firebase:', error);
    }

    // Set current date in the header
    const currentDateElement = document.getElementById('current-date');
    if (currentDateElement) {
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        currentDateElement.textContent = new Date().toLocaleDateString('en-US', options);
    }

    // Check if user is already logged in
    checkLoginStatus();

    // Tab switching functionality
    initTabSwitching();

    // Login form submission
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const rememberMe = document.getElementById('remember-me').checked;

            // Simple validation
            if (!email || !password) {
                showAlert('Please enter both email and password', 'error');
                return;
            }

            // Firebase authentication
            const auth = window.firebaseAuth;

            // Get Firebase functions from the Firebase SDK
            import("https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js").then(firebaseAuth => {
                const { signInWithEmailAndPassword } = firebaseAuth;

                signInWithEmailAndPassword(auth, email, password)
                    .then((userCredential) => {
                        // Signed in
                        const user = userCredential.user;
                        console.log('User logged in:', user);

                        // Get user data from Firestore
                        getUserData(user.uid)
                            .then(userData => {
                                // Login successful
                                login(userData, rememberMe);
                            })
                            .catch(error => {
                                console.error('Error getting user data:', error);
                                // Create basic user data if not found
                                const basicUserData = {
                                    uid: user.uid,
                                    email: user.email,
                                    name: user.displayName || email.split('@')[0],
                                    createdAt: new Date().toISOString()
                                };
                                login(basicUserData, rememberMe);
                            });
                    })
                    .catch((error) => {
                        console.error('Login error:', error);
                        showAlert('Login failed: ' + error.message, 'error');
                    });
            }).catch(error => {
                console.error('Error importing Firebase Auth:', error);
                showAlert('Error during login. Please try again.', 'error');
            });
        });
    }

    // Sign up form submission
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('signup-confirm-password').value;

            // Simple validation
            if (!name || !email || !password || !confirmPassword) {
                showAlert('Please fill in all fields', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showAlert('Passwords do not match', 'error');
                return;
            }

            // Firebase authentication - create user
            const auth = window.firebaseAuth;
            const db = window.firebaseDb;

            // Get Firebase functions from the Firebase SDK
            import("https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js").then(firebaseAuth => {
                const { createUserWithEmailAndPassword, updateProfile } = firebaseAuth;

                createUserWithEmailAndPassword(auth, email, password)
                    .then((userCredential) => {
                        // Signed up
                        const user = userCredential.user;
                        console.log("User created successfully:", user);

                        // Update profile with name
                        updateProfile(user, {
                            displayName: name
                        }).then(() => {
                            console.log('User profile updated');

                            // Import Firestore functions
                            import("https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js").then(firebaseFirestore => {
                                const { doc, setDoc } = firebaseFirestore;

                                // Create user document in Firestore
                                const userData = {
                                    uid: user.uid,
                                    name: name,
                                    email: email,
                                    createdAt: new Date().toISOString()
                                };

                                const userDocRef = doc(db, 'users', user.uid);
                                setDoc(userDocRef, userData)
                                    .then(() => {
                                        console.log('User document created in Firestore');
                                        showAlert('Account created successfully! Please login.', 'success');

                                        // Clear sign up form
                                        signupForm.reset();

                                        // Switch to login tab
                                        document.querySelector('.tab[data-tab="login"]').click();
                                    })
                                    .catch((error) => {
                                        console.error('Error creating user document:', error);
                                        showAlert('Error creating user profile. Please try again.', 'error');
                                    });
                            }).catch(error => {
                                console.error('Error importing Firestore:', error);
                                showAlert('Error creating user profile. Please try again.', 'error');
                            });
                        }).catch((error) => {
                            console.error('Error updating user profile:', error);
                            showAlert('Error updating user profile. Please try again.', 'error');
                        });
                    })
                    .catch((error) => {
                        console.error('Signup error:', error);
                        showAlert('Signup failed: ' + error.message, 'error');
                    });
                }).catch(error => {
                    console.error('Error importing Firebase Auth:', error);
                    showAlert('Error during signup. Please try again.', 'error');
                });
        });
    }

    // Forgot password link
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();

            const email = prompt('Please enter your email address to reset your password:');

            if (email) {
                const auth = window.firebaseAuth;

                // Use dynamic import for Firebase Auth
                import("https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js")
                    .then(firebaseAuth => {
                        const { sendPasswordResetEmail } = firebaseAuth;

                        sendPasswordResetEmail(auth, email)
                            .then(() => {
                                showAlert('Password reset email sent. Please check your inbox.', 'success');
                            })
                            .catch((error) => {
                                console.error('Error sending password reset email:', error);
                                showAlert('Error: ' + error.message, 'error');
                            });
                    })
                    .catch(error => {
                        console.error('Error importing Firebase Auth:', error);
                        showAlert('Error during password reset. Please try again.', 'error');
                    });
            }
        });
    }

    // Logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }

    // Navigation between dashboard sections
    initNavigation();

    // Profile edit buttons
    initProfileEditing();

    // Initialize dashboard cards click handlers
    initDashboardCards();

    // Initialize calculators
    initCalculators();

    // Initialize chatbot
    initChatbot();

    // Initialize "Use my location" buttons
    initLocationButtons();

    // Initialize doctor search functionality
    initDoctorSearch();

    // Initialize maps functionality
    // We'll initialize it regardless of Google Maps API loading status
    // since we have placeholder data that doesn't require the actual API
    initMaps();

    // Initialize blood availability feature
    initBloodAvailabilityFeature();

    // Log a warning if Google Maps API isn't loaded
    if (typeof google === 'undefined' || !google.maps) {
        console.warn('Google Maps API not loaded. Using placeholder data instead.');
    }
});

// Function to check if user is already logged in
function checkLoginStatus() {
    const auth = window.firebaseAuth;

    // Use dynamic import for Firebase Auth
    import("https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js")
        .then(firebaseAuth => {
            const { onAuthStateChanged } = firebaseAuth;

            onAuthStateChanged(auth, (user) => {
                if (user) {
                    // User is signed in
                    console.log('User is signed in:', user);

                    // Get user data from Firestore
                    getUserData(user.uid)
                        .then(userData => {
                            // Update UI with user data
                            updateUIWithUserData(userData);

                            // Show dashboard page
                            showDashboard();
                        })
                        .catch(error => {
                            console.error('Error getting user data:', error);
                            // Create basic user data if not found
                            const basicUserData = {
                                uid: user.uid,
                                email: user.email,
                                name: user.displayName || user.email.split('@')[0]
                            };
                            updateUIWithUserData(basicUserData);
                            showDashboard();
                        });
                } else {
                    // User is signed out
                    console.log('User is signed out');
                }
            });
        })
        .catch(error => {
            console.error('Error importing Firebase Auth:', error);
        });
}

// Function to get user data from Firestore
function getUserData(uid) {
    const db = window.firebaseDb;

    // Use dynamic import for Firestore
    return import("https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js")
        .then(firebaseFirestore => {
            const { doc, getDoc } = firebaseFirestore;

            const userDocRef = doc(db, 'users', uid);
            return getDoc(userDocRef)
                .then((docSnap) => {
                    if (docSnap.exists()) {
                        return docSnap.data();
                    } else {
                        throw new Error('User document not found');
                    }
                });
        })
        .catch(error => {
            console.error('Error importing Firestore:', error);
            throw new Error('Failed to load Firestore module');
        });
}

// Function to update UI with user data
function updateUIWithUserData(userData) {
    // Update user name in header
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        userNameElement.textContent = userData.name || 'Patient';
    }

    // Update profile information
    const profileNameElement = document.getElementById('profile-name');
    if (profileNameElement) {
        profileNameElement.textContent = userData.name || 'Patient';
    }

    const userIdElement = document.getElementById('user-id');
    if (userIdElement) {
        userIdElement.textContent = userData.uid || 'U12345678';
    }

    const profileFullNameInput = document.getElementById('profile-full-name');
    if (profileFullNameInput) {
        profileFullNameInput.value = userData.name || '';
    }

    const profileEmailInput = document.getElementById('profile-email');
    if (profileEmailInput) {
        profileEmailInput.value = userData.email || '';
    }

    const profilePhoneInput = document.getElementById('profile-phone');
    if (profilePhoneInput && userData.phone) {
        profilePhoneInput.value = userData.phone;
    }

    const profileAddressInput = document.getElementById('profile-address');
    if (profileAddressInput && userData.address) {
        profileAddressInput.value = userData.address;
    }

    const profileDobInput = document.getElementById('profile-dob');
    if (profileDobInput && userData.dob) {
        profileDobInput.value = userData.dob;
    }

    const profileGenderSelect = document.getElementById('profile-gender');
    if (profileGenderSelect && userData.gender) {
        profileGenderSelect.value = userData.gender;
    }

    // Update emergency contact information
    if (userData.emergencyContact) {
        const emergencyNameInput = document.getElementById('emergency-name');
        if (emergencyNameInput) {
            emergencyNameInput.value = userData.emergencyContact.name || '';
        }

        const emergencyRelationshipInput = document.getElementById('emergency-relationship');
        if (emergencyRelationshipInput) {
            emergencyRelationshipInput.value = userData.emergencyContact.relationship || '';
        }

        const emergencyPhoneInput = document.getElementById('emergency-phone');
        if (emergencyPhoneInput) {
            emergencyPhoneInput.value = userData.emergencyContact.phone || '';
        }
    }

    // Load user appointments
    loadUserAppointments(userData.uid);
}

// Function to handle login
function login(userData, rememberMe) {
    try {
        console.log('Login function called with user:', userData);

        // Update UI with user data
        updateUIWithUserData(userData);

        // Show dashboard page
        showDashboard();

        // Show success message
        showAlert('Login successful. Welcome back!', 'success');
    } catch (error) {
        console.error('Error in login function:', error);
        showAlert('There was an error logging in. Please try again.', 'error');
    }
}

// Function to show dashboard
function showDashboard() {
    const loginPage = document.getElementById('login-page');
    const dashboardPage = document.getElementById('dashboard-page');

    if (loginPage && dashboardPage) {
        // First hide the login page
        loginPage.classList.remove('active');

        // Then show the dashboard page with a small delay to ensure smooth transition
        setTimeout(() => {
            dashboardPage.classList.add('active');

            // Make sure the dashboard content is shown
            const dashboardContent = document.getElementById('dashboard-content');
            if (dashboardContent) {
                // Hide all content sections first
                const contentSections = document.querySelectorAll('.page-content');
                contentSections.forEach(section => section.classList.remove('active'));

                // Show dashboard content
                dashboardContent.classList.add('active');

                // Set the active navigation link
                const dashboardLink = document.querySelector('.nav-links li[data-page="dashboard-content"]');
                if (dashboardLink) {
                    const allLinks = document.querySelectorAll('.nav-links li');
                    allLinks.forEach(link => link.classList.remove('active'));
                    dashboardLink.classList.add('active');
                }

                // Update URL hash
                window.history.pushState(null, null, '#dashboard-content');

                console.log('Dashboard page and content activated successfully');
            } else {
                console.error('Dashboard content section not found');
            }
        }, 50);
    } else {
        console.error('Login or dashboard page elements not found');
    }
}

// Function to handle logout
function logout() {
    const auth = window.firebaseAuth;

    // Use dynamic import for Firebase Auth
    import("https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js")
        .then(firebaseAuth => {
            const { signOut } = firebaseAuth;

            signOut(auth)
                .then(() => {
                    console.log('User signed out');

                    // Show login page
                    const dashboardPage = document.getElementById('dashboard-page');
                    const loginPage = document.getElementById('login-page');

                    if (dashboardPage && loginPage) {
                        // First hide the dashboard
                        dashboardPage.classList.remove('active');

                        // Then show the login page with a small delay
                        setTimeout(() => {
                            loginPage.classList.add('active');

                            // Clear form fields
                            const emailInput = document.getElementById('login-email');
                            const passwordInput = document.getElementById('login-password');
                            const rememberMeCheckbox = document.getElementById('remember-me');

                            if (emailInput) emailInput.value = '';
                            if (passwordInput) passwordInput.value = '';
                            if (rememberMeCheckbox) rememberMeCheckbox.checked = false;

                            // Make sure login tab is active
                            const loginTab = document.querySelector('.tab[data-tab="login"]');
                            if (loginTab) {
                                loginTab.click();
                            }

                            // Clear URL hash
                            window.history.pushState(null, null, window.location.pathname);

                            // Show success message
                            showAlert('You have been logged out successfully.', 'success');
                        }, 50);
                    }
                })
                .catch((error) => {
                    console.error('Error signing out:', error);
                    showAlert('Error signing out. Please try again.', 'error');
                });
        })
        .catch(error => {
            console.error('Error importing Firebase Auth:', error);
            showAlert('Error during logout. Please try again.', 'error');
        });
}

// Function to initialize tab switching
function initTabSwitching() {
    const tabs = document.querySelectorAll('.tab');
    if (tabs.length > 0) {
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                tabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Hide all tab panes
                const tabPanes = document.querySelectorAll('.tab-pane');
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // Show the corresponding tab pane
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId + '-tab').classList.add('active');
            });
        });
    }

    // Calculator tabs
    const calcTabs = document.querySelectorAll('.calculator-tabs .tab');
    if (calcTabs.length > 0) {
        calcTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                calcTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Hide all calculator panes
                const calcPanes = document.querySelectorAll('.calculator');
                calcPanes.forEach(pane => pane.classList.remove('active'));

                // Show the corresponding calculator pane
                const calcId = this.getAttribute('data-calc');
                document.getElementById(calcId + '-calculator').classList.add('active');
            });
        });
    }
}

// Function to initialize navigation
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-links li');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Prevent default anchor behavior
            e.preventDefault();

            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Hide all content sections
            const contentSections = document.querySelectorAll('.page-content');
            contentSections.forEach(section => section.classList.remove('active'));

            // Show the corresponding content section
            const contentId = this.getAttribute('data-page');
            const contentSection = document.getElementById(contentId);

            if (contentSection) {
                contentSection.classList.add('active');
                console.log('Switched to section:', contentId);

                // Update URL hash for direct linking (but don't trigger a page reload)
                window.history.pushState(null, null, '#' + contentId);
            } else {
                console.error('Content section not found:', contentId);
            }
        });
    });

    // Handle direct navigation via URL hash
    function handleHashChange() {
        const hash = window.location.hash.substring(1); // Remove the # symbol
        if (hash && hash.endsWith('-content')) {
            const targetLink = document.querySelector(`.nav-links li[data-page="${hash}"]`);
            if (targetLink) {
                targetLink.click();
            }
        }
    }

    // Check hash on page load
    handleHashChange();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);
}

// Function to show alerts
function showAlert(message, type = 'info') {
    // For now, we'll use browser alerts
    // In a production app, you would use a nicer UI component
    alert(message);
}

// Function to initialize profile editing
function initProfileEditing() {
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const inputField = this.previousElementSibling;

            if (inputField.disabled) {
                // Enable editing
                inputField.disabled = false;
                inputField.focus();
                this.innerHTML = '<i class="fas fa-save"></i>';
            } else {
                // Save changes
                inputField.disabled = true;
                this.innerHTML = '<i class="fas fa-edit"></i>';

                // If this is the name field, update the display name
                if (inputField.id === 'profile-full-name') {
                    document.getElementById('user-name').textContent = inputField.value.split(' ')[0];
                    document.getElementById('profile-name').textContent = inputField.value;

                    // Update user profile in Firebase
                    const user = firebase.auth().currentUser;
                    if (user) {
                        user.updateProfile({
                            displayName: inputField.value
                        }).then(() => {
                            console.log('User profile updated');

                            // Update user document in Firestore
                            firebase.firestore().collection('users').doc(user.uid).update({
                                name: inputField.value
                            }).then(() => {
                                console.log('User document updated');
                                showAlert('Profile updated successfully', 'success');
                            }).catch((error) => {
                                console.error('Error updating user document:', error);
                                showAlert('Error updating profile', 'error');
                            });
                        }).catch((error) => {
                            console.error('Error updating user profile:', error);
                            showAlert('Error updating profile', 'error');
                        });
                    }
                } else {
                    // Update other fields in Firestore
                    const user = firebase.auth().currentUser;
                    if (user) {
                        const fieldId = inputField.id;
                        let fieldName = '';
                        let fieldValue = inputField.value;

                        // Map field IDs to Firestore field names
                        switch (fieldId) {
                            case 'profile-email':
                                fieldName = 'email';
                                break;
                            case 'profile-phone':
                                fieldName = 'phone';
                                break;
                            case 'profile-address':
                                fieldName = 'address';
                                break;
                            case 'profile-dob':
                                fieldName = 'dob';
                                break;
                            case 'profile-gender':
                                fieldName = 'gender';
                                break;
                            case 'emergency-name':
                                fieldName = 'emergencyContact.name';
                                break;
                            case 'emergency-relationship':
                                fieldName = 'emergencyContact.relationship';
                                break;
                            case 'emergency-phone':
                                fieldName = 'emergencyContact.phone';
                                break;
                        }

                        if (fieldName) {
                            // Handle nested fields
                            if (fieldName.includes('.')) {
                                const [parent, child] = fieldName.split('.');
                                const updateData = {};
                                updateData[parent] = firebase.firestore.FieldValue.delete();

                                // First delete the parent field to avoid merge issues
                                firebase.firestore().collection('users').doc(user.uid).update(updateData)
                                    .then(() => {
                                        // Then recreate it with the new data
                                        const newData = {};
                                        newData[parent] = {};
                                        newData[parent][child] = fieldValue;

                                        firebase.firestore().collection('users').doc(user.uid).update(newData)
                                            .then(() => {
                                                console.log('User document updated');
                                                showAlert('Profile updated successfully', 'success');
                                            }).catch((error) => {
                                                console.error('Error updating user document:', error);
                                                showAlert('Error updating profile', 'error');
                                            });
                                    }).catch((error) => {
                                        console.error('Error updating user document:', error);
                                        showAlert('Error updating profile', 'error');
                                    });
                            } else {
                                // Simple field update
                                const updateData = {};
                                updateData[fieldName] = fieldValue;

                                firebase.firestore().collection('users').doc(user.uid).update(updateData)
                                    .then(() => {
                                        console.log('User document updated');
                                        showAlert('Profile updated successfully', 'success');
                                    }).catch((error) => {
                                        console.error('Error updating user document:', error);
                                        showAlert('Error updating profile', 'error');
                                    });
                            }
                        }
                    }
                }
            }
        });
    });
// Blood Availability Feature - Optimized Implementation
function initBloodAvailabilityFeature() {
    // Initialize card navigation
    const bloodCard = document.getElementById('blood-availability-card');
    if (bloodCard) {
        bloodCard.addEventListener('click', function() {
            // Navigate to blood availability section
            const navLink = document.querySelector('.nav-links li[data-page="blood-availability-content"]');
            if (navLink) {
                navLink.click();
            }
        });
    }

    // Initialize location button
    const useLocBtn = document.getElementById('use-blood-location-btn');
    const locInput = document.getElementById('blood-location-input');
    if (useLocBtn && locInput) {
        useLocBtn.addEventListener('click', function() {
            getCurrentLocationWithAddress(useLocBtn, locInput);
        });
    }

    // Initialize search functionality
    const searchBtn = document.getElementById('search-blood-btn');
    const groupSelect = document.getElementById('blood-group-select');
    const resultsDiv = document.getElementById('blood-results');

    if (searchBtn && groupSelect && locInput && resultsDiv) {
        searchBtn.addEventListener('click', async function() {
            try {
                const group = groupSelect.value;
                const location = locInput.value.trim();

                if (!location) {
                    showAlert('Please enter a location or use current location.', 'error');
                    return;
                }

                // Show loading state
                searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
                searchBtn.disabled = true;
                resultsDiv.innerHTML = '<div class="loading-message"><i class="fas fa-spinner fa-spin"></i> Searching for blood banks...</div>';

                let userLat = null, userLng = null;
                if (locInput.dataset.lat && locInput.dataset.lng) {
                    userLat = parseFloat(locInput.dataset.lat);
                    userLng = parseFloat(locInput.dataset.lng);
                }

                // Use local database instead of Firestore
                if (!window.localDB || !window.localDB.loaded) {
                    throw new Error('Local database not loaded');
                }

                // Search blood banks using local database
                const banks = window.localDB.searchBloodBanks(group, location, userLat, userLng);

                // Display results
                displayBloodBankResults(banks, group, resultsDiv);

            } catch (error) {
                console.error('Error searching blood banks:', error);
                resultsDiv.innerHTML = '<div class="error-message"><i class="fas fa-exclamation-triangle"></i> Error searching blood banks. Please try again.</div>';
                showAlert('Error searching blood banks. Please try again.', 'error');
            } finally {
                // Reset button state
                searchBtn.innerHTML = 'Search Blood Banks';
                searchBtn.disabled = false;
            }
        });
    }
}

// Display blood bank search results
function displayBloodBankResults(banks, selectedGroup, resultsDiv) {
    if (banks.length === 0) {
        resultsDiv.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No Blood Banks Found</h3>
                <p>No blood banks found with the selected criteria. Try expanding your search area or selecting a different blood group.</p>
            </div>
        `;
        return;
    }

    let html = `<div class="results-header"><h3>Found ${banks.length} Blood Bank${banks.length > 1 ? 's' : ''}</h3></div>`;

    banks.forEach(bank => {
        const stockEntries = Object.entries(bank.blood_stock || {});
        const hasSelectedGroup = selectedGroup && bank.blood_stock && bank.blood_stock[selectedGroup] > 0;

        html += `
            <div class="hospital-card blood-bank-card ${hasSelectedGroup ? 'has-selected-group' : ''}">
                <div class="bank-header">
                    <h3>${bank.bank_name || 'Unknown Bank'}</h3>
                    ${hasSelectedGroup ? `<span class="availability-badge">${selectedGroup} Available</span>` : ''}
                </div>
                <div class="bank-details">
                    <p><i class="fas fa-map-marker-alt"></i> ${bank.address || 'Address not available'}</p>
                    <p><i class="fas fa-phone"></i> ${bank.contact_number || 'Contact not available'}</p>
                    ${bank.distance !== null ? `<p><i class="fas fa-route"></i> ${bank.distance.toFixed(2)} km away</p>` : ''}
                    ${bank.email ? `<p><i class="fas fa-envelope"></i> ${bank.email}</p>` : ''}
                </div>
                <div class="blood-stock">
                    <strong><i class="fas fa-tint"></i> Blood Stock:</strong>
                    <div class="stock-grid">
                        ${stockEntries.length > 0 ?
                            stockEntries.map(([type, units]) =>
                                `<div class="stock-item ${type === selectedGroup ? 'highlighted' : ''} ${units > 0 ? 'available' : 'unavailable'}">
                                    <span class="blood-type">${type}</span>
                                    <span class="units">${units} units</span>
                                </div>`
                            ).join('') :
                            '<div class="no-stock">No stock information available</div>'
                        }
                    </div>
                </div>
                <div class="bank-footer">
                    <p class="last-updated">
                        <i class="fas fa-clock"></i>
                        Last updated: ${bank.last_updated ? new Date(bank.last_updated).toLocaleString() : 'N/A'}
                    </p>
                    <div class="bank-actions">
                        <button class="btn-outline" onclick="callBloodBank('${bank.contact_number || ''}')">
                            <i class="fas fa-phone"></i> Call
                        </button>
                        ${bank.latitude && bank.longitude ?
                            `<button class="btn-outline" onclick="openDirections(${bank.latitude}, ${bank.longitude})">
                                <i class="fas fa-directions"></i> Directions
                            </button>` : ''
                        }
                    </div>
                </div>
            </div>
        `;
    });

    resultsDiv.innerHTML = html;
}

// Helper function to call blood bank
function callBloodBank(phoneNumber) {
    if (phoneNumber) {
        window.location.href = `tel:${phoneNumber}`;
    } else {
        showAlert('Phone number not available', 'error');
    }
}

// Helper function to open directions
function openDirections(lat, lng) {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
    window.open(url, '_blank');
}

// Enhanced location functionality with Google Maps API
function getCurrentLocationWithAddress(button, input) {
    if (!navigator.geolocation) {
        showAlert('Geolocation is not supported by your browser.', 'error');
        return;
    }

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Locating...';
    button.disabled = true;

    navigator.geolocation.getCurrentPosition(
        function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            // Store coordinates
            input.dataset.lat = lat;
            input.dataset.lng = lng;

            // Try to get readable address using Google Maps Geocoding API
            if (typeof google !== 'undefined' && google.maps) {
                const geocoder = new google.maps.Geocoder();
                const latlng = { lat: lat, lng: lng };

                geocoder.geocode({ location: latlng }, function(results, status) {
                    if (status === 'OK' && results[0]) {
                        input.value = results[0].formatted_address;
                    } else {
                        input.value = `Current Location (${lat.toFixed(4)}, ${lng.toFixed(4)})`;
                    }

                    button.innerHTML = '<i class="fas fa-map-marker-alt"></i> Use My Current Location';
                    button.disabled = false;
                    showAlert('Location detected successfully!', 'success');
                });
            } else {
                // Fallback if Google Maps API is not loaded
                input.value = `Current Location (${lat.toFixed(4)}, ${lng.toFixed(4)})`;
                button.innerHTML = '<i class="fas fa-map-marker-alt"></i> Use My Current Location';
                button.disabled = false;
                showAlert('Location detected successfully!', 'success');
            }
        },
        function(error) {
            console.error('Geolocation error:', error);
            let errorMessage = 'Unable to get your location. ';

            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'Please allow location access.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'Location information unavailable.';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'Location request timed out.';
                    break;
                default:
                    errorMessage += 'Please enter manually.';
                    break;
            }

            showAlert(errorMessage, 'error');
            button.innerHTML = '<i class="fas fa-map-marker-alt"></i> Use My Current Location';
            button.disabled = false;
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
        }
    );
}

// Initialize Google Maps (callback function)
function initMap() {
    console.log('Google Maps API loaded successfully');
    // You can add additional map initialization here if needed
}

// Haversine formula to calculate distance (in km)
function haversine(lat1, lon1, lat2, lon2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
}


    // Change password button
    const changePasswordBtn = document.getElementById('change-password-btn');
    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', function() {
            const auth = window.firebaseAuth;
            const user = auth.currentUser;

            if (user) {
                const email = user.email;

                // Use dynamic import for Firebase Auth
                import("https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js")
                    .then(firebaseAuth => {
                        const { sendPasswordResetEmail } = firebaseAuth;

                        sendPasswordResetEmail(auth, email)
                            .then(() => {
                                showAlert('Password reset email sent to ' + email, 'success');
                            })
                            .catch((error) => {
                                console.error('Error sending password reset email:', error);
                                showAlert('Error: ' + error.message, 'error');
                            });
                    })
                    .catch(error => {
                        console.error('Error importing Firebase Auth:', error);
                        showAlert('Error during password reset. Please try again.', 'error');
                    });
            } else {
                showAlert('You must be logged in to change your password', 'error');
            }
        });
    }
}

// Function to initialize dashboard cards
function initDashboardCards() {
    const cards = document.querySelectorAll('.dashboard-cards .card');
    cards.forEach(card => {
        card.addEventListener('click', function() {
            const cardTitle = this.querySelector('h3').textContent.trim();
            let targetSection = '';

            // Map card titles to their corresponding sections
            switch (cardTitle) {
                case 'Book Appointment':
                    targetSection = 'book-appointment-content';
                    break;
                case 'Hospitals Near Me':
                    targetSection = 'hospitals-content';
                    break;
                case 'Pharmacy Locator':
                    targetSection = 'pharmacy-content';
                    break;
                case 'Symptom Checker':
                    targetSection = 'chatbot-content';
                    break;
                default:
                    targetSection = 'dashboard-content';
            }

            // Click the corresponding navigation link
            if (targetSection) {
                const navLink = document.querySelector(`.nav-links li[data-page="${targetSection}"]`);
                if (navLink) {
                    navLink.click();
                }
            }
        });
    });

    // Book now link in appointments section
    const bookNowLink = document.querySelector('.book-now-link');
    if (bookNowLink) {
        bookNowLink.addEventListener('click', function(e) {
            e.preventDefault();
            const navLink = document.querySelector('.nav-links li[data-page="book-appointment-content"]');
            if (navLink) {
                navLink.click();
            }
        });
    }
}

// Function to initialize calculators
function initCalculators() {
    // BMI Calculator
    const calculateBmiBtn = document.getElementById('calculate-bmi');
    if (calculateBmiBtn) {
        calculateBmiBtn.addEventListener('click', function() {
            const height = parseFloat(document.getElementById('height').value);
            const weight = parseFloat(document.getElementById('weight').value);
            const resultElement = document.getElementById('bmi-result');

            if (!height || !weight) {
                resultElement.innerHTML = '<p class="error">Please enter both height and weight.</p>';
                return;
            }

            // Calculate BMI: weight (kg) / (height (m))^2
            const heightInMeters = height / 100;
            const bmi = weight / (heightInMeters * heightInMeters);
            const roundedBmi = Math.round(bmi * 10) / 10;

            let category = '';
            let categoryClass = '';

            if (bmi < 18.5) {
                category = 'Underweight';
                categoryClass = 'underweight';
            } else if (bmi >= 18.5 && bmi < 25) {
                category = 'Normal weight';
                categoryClass = 'normal';
            } else if (bmi >= 25 && bmi < 30) {
                category = 'Overweight';
                categoryClass = 'overweight';
            } else {
                category = 'Obese';
                categoryClass = 'obese';
            }

            resultElement.innerHTML = `
                <p>Your BMI: <strong>${roundedBmi}</strong></p>
                <p>Category: <span class="${categoryClass}">${category}</span></p>
                <p>A healthy BMI ranges from 18.5 to 24.9.</p>
            `;
        });
    }

    // Calorie Calculator
    const calculateCaloriesBtn = document.getElementById('calculate-calories');
    if (calculateCaloriesBtn) {
        calculateCaloriesBtn.addEventListener('click', function() {
            const resultElement = document.getElementById('calorie-result');
            let totalCalories = 0;
            let selectedItems = [];

            // Calorie values for Indian food items (approximate per serving)
            const calorieValues = {
                'roti': 120,
                'rice': 200,
                'dal': 150,
                'sabzi': 100,
                'paneer': 300,
                'chicken': 250,
                'dosa': 180,
                'idli': 80
            };

            // Check each food item
            Object.keys(calorieValues).forEach(food => {
                const checkbox = document.getElementById(food);
                if (checkbox && checkbox.checked) {
                    const qtyInput = document.getElementById(`${food}-qty`);
                    const qty = qtyInput ? parseInt(qtyInput.value) || 1 : 1;
                    const calories = calorieValues[food] * qty;

                    totalCalories += calories;
                    selectedItems.push({
                        name: food.charAt(0).toUpperCase() + food.slice(1),
                        quantity: qty,
                        calories: calories
                    });
                }
            });

            if (selectedItems.length === 0) {
                resultElement.innerHTML = '<p class="error">Please select at least one food item.</p>';
                return;
            }

            // Generate result HTML
            let resultHTML = `
                <p>Total Calories: <strong>${totalCalories}</strong></p>
                <h4>Breakdown:</h4>
                <ul>
            `;

            selectedItems.forEach(item => {
                resultHTML += `<li>${item.name} (${item.quantity}): ${item.calories} calories</li>`;
            });

            resultHTML += '</ul>';

            // Add dietary advice based on calorie count
            if (totalCalories < 500) {
                resultHTML += '<p>This is a light meal or snack.</p>';
            } else if (totalCalories < 800) {
                resultHTML += '<p>This is a moderate meal.</p>';
            } else {
                resultHTML += '<p>This is a heavy meal. Consider portion control if you\'re watching your calorie intake.</p>';
            }

            resultElement.innerHTML = resultHTML;
        });
    }
}

// Function to initialize chatbot
function initChatbot() {
    const sendMessageBtn = document.getElementById('send-message');
    const userMessageInput = document.getElementById('user-message');
    const chatMessages = document.getElementById('chat-messages');

    if (sendMessageBtn && userMessageInput && chatMessages) {
        // Function to add a message to the chat
        function addMessage(message, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = `<p>${message}</p>`;

            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Chatbot state
        let chatbotState = {
            awaitingBookingConfirmation: false,
            recommendedSpecialty: '',
            symptomDescription: ''
        };

        // Enhanced symptom checker logic
        function processSymptoms(message) {
            const userInput = message.toLowerCase();

            // Check if we're waiting for booking confirmation
            if (chatbotState.awaitingBookingConfirmation) {
                // Check if user wants to book an appointment
                if (userInput.includes('yes') || userInput.includes('book') || userInput.includes('sure') || userInput.includes('ok')) {
                    // Reset state
                    chatbotState.awaitingBookingConfirmation = false;

                    // Navigate to appointment booking page
                    setTimeout(() => {
                        const navLink = document.querySelector('.nav-links li[data-page="book-appointment-content"]');
                        if (navLink) {
                            navLink.click();

                            // Pre-fill specialty if we have a recommendation
                            if (chatbotState.recommendedSpecialty) {
                                setTimeout(() => {
                                    const specialtySelect = document.getElementById('specialty');
                                    if (specialtySelect) {
                                        specialtySelect.value = chatbotState.recommendedSpecialty;
                                    }

                                    // Set location to current location
                                    const locationInput = document.getElementById('location');
                                    if (locationInput) {
                                        locationInput.value = 'Current Location';
                                    }

                                    // Click search button
                                    setTimeout(() => {
                                        const searchButton = document.getElementById('search-doctors');
                                        if (searchButton) {
                                            searchButton.click();
                                        }
                                    }, 500);
                                }, 500);
                            }
                        }
                    }, 1000);

                    return "I'll help you book an appointment right away. I'm taking you to our appointment booking page where you can select a doctor and time slot. Based on your symptoms, I've pre-selected the appropriate specialty for you.";
                } else if (userInput.includes('no') || userInput.includes('not') || userInput.includes('later')) {
                    // Reset state
                    chatbotState.awaitingBookingConfirmation = false;
                    return "No problem. If you change your mind, you can book an appointment anytime from the dashboard. Is there anything else I can help you with?";
                } else {
                    // User didn't clearly say yes or no, ask again
                    return "I'm not sure if you want to book an appointment. Please say 'yes' if you'd like to book an appointment now, or 'no' if you prefer to do it later.";
                }
            }

            // Store the symptom description
            chatbotState.symptomDescription = message;

            // Check for common symptoms and provide responses
            if (userInput.includes('fever') || userInput.includes('temperature')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'general-physician';
                return "Fever can be a sign of infection. Monitor your temperature and stay hydrated. If your fever is high (above 102°F or 39°C) or persists for more than 2 days, you should consult a doctor. Would you like me to help you book an appointment with a general physician now?";
            } else if (userInput.includes('headache') || userInput.includes('head pain') || userInput.includes('migraine')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'neurologist';
                return "Headaches can be caused by stress, dehydration, or lack of sleep. Try resting in a quiet, dark room and staying hydrated. If your headache is severe or persistent, you should consult a doctor. Would you like me to help you book an appointment with a neurologist now?";
            } else if (userInput.includes('cough') || userInput.includes('sore throat') || userInput.includes('cold') || userInput.includes('flu')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'general-physician';
                return "Coughs and sore throats are common symptoms of respiratory infections. Rest, stay hydrated, and consider over-the-counter remedies. If symptoms persist for more than a week or are severe, you should consult a doctor. Would you like me to help you book an appointment with a general physician now?";
            } else if (userInput.includes('stomach') || userInput.includes('nausea') || userInput.includes('vomit') || userInput.includes('diarrhea')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'general-physician';
                return "Stomach issues can be caused by food poisoning, viruses, or digestive problems. Stay hydrated and stick to bland foods. If symptoms are severe or persist for more than 2 days, you should consult a doctor. Would you like me to help you book an appointment with a general physician now?";
            } else if (userInput.includes('heart') || userInput.includes('chest pain') || userInput.includes('palpitations')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'cardiologist';
                return "Chest pain or heart palpitations can be serious symptoms that require medical attention. If you're experiencing severe chest pain, please call emergency services immediately. For less severe symptoms, would you like me to help you book an appointment with a cardiologist now?";
            } else if (userInput.includes('skin') || userInput.includes('rash') || userInput.includes('acne')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'dermatologist';
                return "Skin conditions can have many causes. For persistent rashes, severe acne, or other skin concerns, it's best to consult with a dermatologist. Would you like me to help you book an appointment with a dermatologist now?";
            } else if (userInput.includes('child') || userInput.includes('baby') || userInput.includes('kid')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'pediatrician';
                return "For children's health concerns, it's best to consult with a pediatrician. Would you like me to help you book an appointment with a pediatrician now?";
            } else if (userInput.includes('bone') || userInput.includes('joint') || userInput.includes('fracture') || userInput.includes('sprain')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'orthopedic';
                return "Bone and joint issues should be evaluated by an orthopedic specialist. Would you like me to help you book an appointment with an orthopedic doctor now?";
            } else if (userInput.includes('book appointment') || userInput.includes('see doctor') || userInput.includes('schedule appointment')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'general-physician';
                return "I'd be happy to help you book an appointment. Based on your request, I'll connect you with a general physician. Would you like to proceed with booking now?";
            } else if (userInput.includes('pain')) {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'general-physician';
                return "Pain can have many causes. Rest the affected area and consider over-the-counter pain relievers. If the pain is severe, sudden, or persistent, you should consult a doctor. Would you like me to help you book an appointment with a general physician now?";
            } else {
                chatbotState.awaitingBookingConfirmation = true;
                chatbotState.recommendedSpecialty = 'general-physician';
                return "I'm not able to provide specific advice for those symptoms. For accurate diagnosis and treatment, it's best to consult with a healthcare professional. Would you like me to help you book an appointment with a general physician now?";
            }
        }

        // Send message when button is clicked
        sendMessageBtn.addEventListener('click', function() {
            const message = userMessageInput.value.trim();
            if (message) {
                // Add user message to chat
                addMessage(message, true);

                // Clear input
                userMessageInput.value = '';

                // Process the message and respond (with a small delay to seem more natural)
                setTimeout(() => {
                    const response = processSymptoms(message);
                    addMessage(response);
                }, 1000);
            }
        });

        // Send message when Enter key is pressed
        userMessageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessageBtn.click();
                e.preventDefault();
            }
        });
    }
}

// Function to initialize doctor search
function initDoctorSearch() {
    const searchDoctorsBtn = document.getElementById('search-doctors');
    const doctorsList = document.getElementById('doctors-list');

    if (searchDoctorsBtn && doctorsList) {
        searchDoctorsBtn.addEventListener('click', async function() {
            try {
                // Get search parameters
                const specialty = document.getElementById('specialty').value;
                const location = document.getElementById('location').value;
                const date = document.getElementById('appointment-date').value;

                if (!location) {
                    showAlert('Please enter a location', 'error');
                    return;
                }

                // Show loading state
                searchDoctorsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
                searchDoctorsBtn.disabled = true;
                doctorsList.innerHTML = '<div class="loading-message"><i class="fas fa-spinner fa-spin"></i> Searching for doctors...</div>';

                // Get user location if available
                let userLat = null, userLng = null;
                const locationInput = document.getElementById('location');
                if (locationInput.dataset.lat && locationInput.dataset.lng) {
                    userLat = parseFloat(locationInput.dataset.lat);
                    userLng = parseFloat(locationInput.dataset.lng);
                }

                // Use local database
                if (!window.localDB || !window.localDB.loaded) {
                    throw new Error('Local database not loaded');
                }

                // Search doctors using local database
                const doctors = window.localDB.searchDoctors(specialty, location, date, userLat, userLng);

                if (doctors.length === 0) {
                    doctorsList.innerHTML = '<div class="no-results"><i class="fas fa-user-md"></i><h3>No Doctors Found</h3><p>No doctors found matching your criteria. Try expanding your search area or selecting a different specialty.</p></div>';
                    return;
                }

                // Generate HTML for doctors
                displayDoctorResults(doctors, doctorsList);

            } catch (error) {
                console.error('Error searching doctors:', error);
                doctorsList.innerHTML = '<div class="error-message"><i class="fas fa-exclamation-triangle"></i> Error searching doctors. Please try again.</div>';
                showAlert('Error searching doctors. Please try again.', 'error');
            } finally {
                // Reset button state
                searchDoctorsBtn.innerHTML = 'Search';
                searchDoctorsBtn.disabled = false;
            }
        });
    }
}

// Display doctor search results
function displayDoctorResults(doctors, container) {
    let doctorsHTML = `<div class="results-header"><h3>Found ${doctors.length} Doctor${doctors.length > 1 ? 's' : ''}</h3></div>`;

    doctors.forEach(doctor => {
        // Generate available time slots
        let slotsHTML = '';
        doctor.available_times.forEach(time => {
            slotsHTML += `<div class="time-slot" data-time="${time}">${time}</div>`;
        });

        // Format specialty name
        const specialtyName = doctor.specialty.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());

        doctorsHTML += `
            <div class="doctor-card" data-doctor-id="${doctor.id}">
                <div class="doctor-header">
                    <div class="doctor-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div class="doctor-info">
                        <h3>${doctor.name}</h3>
                        <p class="specialty">${specialtyName}</p>
                        <p class="qualification">${doctor.qualification}</p>
                        <div class="doctor-rating">
                            <span class="stars">${'★'.repeat(Math.floor(doctor.rating))}${'☆'.repeat(5 - Math.floor(doctor.rating))}</span>
                            <span class="rating-value">${doctor.rating}</span>
                            <span class="reviews">(${doctor.total_reviews} reviews)</span>
                        </div>
                    </div>
                </div>
                <div class="doctor-details">
                    <p><i class="fas fa-hospital"></i> ${doctor.hospital_name}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${doctor.hospital_address}</p>
                    <p><i class="fas fa-phone"></i> ${doctor.contact_number}</p>
                    <p><i class="fas fa-graduation-cap"></i> ${doctor.experience} years experience</p>
                    <p><i class="fas fa-rupee-sign"></i> ₹${doctor.consultation_fee} consultation fee</p>
                    ${doctor.distance ? `<p><i class="fas fa-route"></i> ${doctor.distance.toFixed(2)} km away</p>` : ''}
                </div>
                <div class="doctor-about">
                    <p><strong>About:</strong> ${doctor.about}</p>
                    <p><strong>Languages:</strong> ${doctor.languages.join(', ')}</p>
                </div>
                <div class="time-slots">
                    <h4>Available Slots</h4>
                    <div class="slots-container">
                        ${slotsHTML}
                    </div>
                </div>
                <div class="doctor-actions">
                    <button class="btn-outline view-profile-btn">View Profile</button>
                    <button class="btn-primary book-appointment-btn">Book Appointment</button>
                </div>
            </div>
        `;
    });

    container.innerHTML = doctorsHTML;

    // Add event listeners to time slots
    const timeSlots = container.querySelectorAll('.time-slot');
    timeSlots.forEach(slot => {
        slot.addEventListener('click', function() {
            // Remove selected class from all slots in this doctor card
            const doctorCard = this.closest('.doctor-card');
            const slots = doctorCard.querySelectorAll('.time-slot');
            slots.forEach(s => s.classList.remove('selected'));

            // Add selected class to clicked slot
            this.classList.add('selected');
        });
    });

    // Add event listeners to book appointment buttons
    const bookButtons = container.querySelectorAll('.book-appointment-btn');
    bookButtons.forEach(button => {
        button.addEventListener('click', function() {
            const doctorCard = this.closest('.doctor-card');
            const doctorId = doctorCard.getAttribute('data-doctor-id');
            const doctor = window.localDB.getDoctorById(doctorId);
            const selectedSlot = doctorCard.querySelector('.time-slot.selected');

            if (selectedSlot) {
                const slotTime = selectedSlot.getAttribute('data-time');
                const appointmentDate = document.getElementById('appointment-date').value || new Date().toISOString().split('T')[0];

                // Get current user
                const auth = window.firebaseAuth;
                const user = auth.currentUser;

                if (user) {
                    // Create appointment object
                    const appointment = {
                        doctorId: doctor.id,
                        doctorName: doctor.name,
                        specialty: doctor.specialty,
                        hospital: doctor.hospital_name,
                        location: doctor.hospital_address,
                        date: appointmentDate,
                        time: slotTime,
                        fee: doctor.consultation_fee,
                        createdAt: new Date().toISOString()
                    };

                    // Save appointment
                    AppointmentManager.saveAppointment(user.uid, appointment);

                    // Show success message
                    showAlert(`Appointment booked with ${doctor.name} at ${slotTime} on ${appointmentDate}`, 'success');

                    // Navigate to dashboard to see the appointment
                    setTimeout(() => {
                        const dashboardLink = document.querySelector('.nav-links li[data-page="dashboard-content"]');
                        if (dashboardLink) {
                            dashboardLink.click();
                            // Reload appointments
                            loadUserAppointments(user.uid);
                        }
                    }, 1500);
                } else {
                    showAlert('You must be logged in to book an appointment', 'error');
                }
            } else {
                showAlert('Please select a time slot first', 'error');
            }
        });
    });
}

// Function to initialize maps
function initMaps() {
    // This function would initialize Google Maps for hospitals and pharmacies
    // For now, we'll just add placeholder functionality

    // Hospital map
    const hospitalsMap = document.getElementById('hospitals-map');
    const searchHospitalsBtn = document.getElementById('search-hospitals');
    const hospitalsList = document.getElementById('hospitals-list');

    if (hospitalsMap && searchHospitalsBtn && hospitalsList) {
        searchHospitalsBtn.addEventListener('click', function() {
            // In a real app, this would use the Google Maps API to find hospitals
            // For now, we'll just add some placeholder data

            const hospitalType = document.getElementById('hospital-type').value;
            const location = document.getElementById('hospital-location').value;

            if (!location) {
                showAlert('Please enter a location', 'error');
                return;
            }

            // Show loading state
            hospitalsList.innerHTML = '<p>Searching for hospitals...</p>';

            // Simulate API call delay
            setTimeout(() => {
                // Sample hospital data
                const hospitals = [
                    {
                        name: 'City General Hospital',
                        address: '123 Main St, ' + location,
                        phone: '(*************',
                        types: ['general', 'emergency'],
                        distance: '1.2 miles'
                    },
                    {
                        name: 'Community Medical Center',
                        address: '456 Oak Ave, ' + location,
                        phone: '(*************',
                        types: ['general', 'maternity'],
                        distance: '2.5 miles'
                    },
                    {
                        name: 'Children\'s Hospital',
                        address: '789 Pine Rd, ' + location,
                        phone: '(*************',
                        types: ['pediatric'],
                        distance: '3.8 miles'
                    }
                ];

                // Filter by type if selected
                const filteredHospitals = hospitalType ?
                    hospitals.filter(h => h.types.includes(hospitalType)) :
                    hospitals;

                if (filteredHospitals.length === 0) {
                    hospitalsList.innerHTML = '<p>No hospitals found matching your criteria.</p>';
                    return;
                }

                // Generate HTML for hospitals
                let hospitalsHTML = '';
                filteredHospitals.forEach(hospital => {
                    let typesHTML = '';
                    hospital.types.forEach(type => {
                        typesHTML += `<span class="hospital-type ${type}">${type}</span>`;
                    });

                    hospitalsHTML += `
                        <div class="hospital-card">
                            <h3>${hospital.name}</h3>
                            <p><i class="fas fa-map-marker-alt"></i> ${hospital.address}</p>
                            <p><i class="fas fa-phone"></i> ${hospital.phone}</p>
                            <p><i class="fas fa-road"></i> ${hospital.distance}</p>
                            <p>${typesHTML}</p>
                        </div>
                    `;
                });

                hospitalsList.innerHTML = hospitalsHTML;
            }, 1500);
        });
    }

    // Pharmacy map
    const pharmaciesMap = document.getElementById('pharmacies-map');
    const searchPharmaciesBtn = document.getElementById('search-pharmacies');
    const pharmaciesList = document.getElementById('pharmacies-list');

    if (pharmaciesMap && searchPharmaciesBtn && pharmaciesList) {
        searchPharmaciesBtn.addEventListener('click', function() {
            // In a real app, this would use the Google Maps API to find pharmacies
            // For now, we'll just add some placeholder data

            const location = document.getElementById('pharmacy-location').value;
            const medicine = document.getElementById('medicine-search').value;

            if (!location) {
                showAlert('Please enter a location', 'error');
                return;
            }

            // Show loading state
            pharmaciesList.innerHTML = '<p>Searching for pharmacies...</p>';

            // Simulate API call delay
            setTimeout(() => {
                // Sample pharmacy data
                const pharmacies = [
                    {
                        name: 'City Pharmacy',
                        address: '123 Main St, ' + location,
                        phone: '(*************',
                        hours: '8:00 AM - 10:00 PM',
                        distance: '0.8 miles',
                        hasMedicine: Math.random() > 0.3 // Random availability
                    },
                    {
                        name: 'Health Plus Pharmacy',
                        address: '456 Oak Ave, ' + location,
                        phone: '(*************',
                        hours: '24 hours',
                        distance: '1.5 miles',
                        hasMedicine: Math.random() > 0.3 // Random availability
                    },
                    {
                        name: 'Community Drugstore',
                        address: '789 Pine Rd, ' + location,
                        phone: '(*************',
                        hours: '9:00 AM - 9:00 PM',
                        distance: '2.2 miles',
                        hasMedicine: Math.random() > 0.3 // Random availability
                    }
                ];

                // Generate HTML for pharmacies
                let pharmaciesHTML = '';
                pharmacies.forEach(pharmacy => {
                    let medicineHTML = '';
                    if (medicine) {
                        medicineHTML = pharmacy.hasMedicine ?
                            `<p class="medicine-available"><i class="fas fa-check-circle"></i> ${medicine} is available</p>` :
                            `<p class="medicine-unavailable"><i class="fas fa-times-circle"></i> ${medicine} is not available</p>`;
                    }

                    pharmaciesHTML += `
                        <div class="pharmacy-card">
                            <h3>${pharmacy.name}</h3>
                            <p><i class="fas fa-map-marker-alt"></i> ${pharmacy.address}</p>
                            <p><i class="fas fa-phone"></i> ${pharmacy.phone}</p>
                            <p><i class="fas fa-clock"></i> ${pharmacy.hours}</p>
                            <p><i class="fas fa-road"></i> ${pharmacy.distance}</p>
                            ${medicineHTML}
                        </div>
                    `;
                });

                pharmaciesList.innerHTML = pharmaciesHTML;
            }, 1500);
        });
    }
}

// Function to initialize location buttons
function initLocationButtons() {
    // Doctor appointment location button
    const useMyLocationBtn = document.getElementById('use-my-location');
    if (useMyLocationBtn) {
        useMyLocationBtn.addEventListener('click', function() {
            const locationInput = document.getElementById('location');
            if (locationInput) {
                // In a real app, this would use the Geolocation API
                // For now, we'll just set a placeholder value
                locationInput.value = 'Current Location';
                showAlert('Location set to: Current Location', 'success');
            }
        });
    }

    // Hospital location button
    const hospitalLocationBtn = document.getElementById('hospital-use-my-location');
    if (hospitalLocationBtn) {
        hospitalLocationBtn.addEventListener('click', function() {
            const locationInput = document.getElementById('hospital-location');
            if (locationInput) {
                locationInput.value = 'Current Location';
                showAlert('Location set to: Current Location', 'success');
            }
        });
    }

    // Pharmacy location button
    const pharmacyLocationBtn = document.getElementById('pharmacy-use-my-location');
    if (pharmacyLocationBtn) {
        pharmacyLocationBtn.addEventListener('click', function() {
            const locationInput = document.getElementById('pharmacy-location');
            if (locationInput) {
                locationInput.value = 'Current Location';
                showAlert('Location set to: Current Location', 'success');
            }
        });
    }
}

// Appointment Management System
const AppointmentManager = {
    // Get all appointments for a user
    getAppointments: function(userId) {
        const appointmentsJSON = localStorage.getItem(`appointments_${userId}`);
        return appointmentsJSON ? JSON.parse(appointmentsJSON) : [];
    },

    // Save an appointment
    saveAppointment: function(userId, appointment) {
        // Get existing appointments
        const appointments = this.getAppointments(userId);

        // Add new appointment with unique ID
        appointment.id = Date.now().toString();
        appointment.status = 'confirmed';
        appointments.push(appointment);

        // Save back to localStorage
        localStorage.setItem(`appointments_${userId}`, JSON.stringify(appointments));

        return appointment;
    },

    // Cancel an appointment
    cancelAppointment: function(userId, appointmentId) {
        const appointments = this.getAppointments(userId);
        const index = appointments.findIndex(a => a.id === appointmentId);

        if (index !== -1) {
            appointments[index].status = 'cancelled';
            localStorage.setItem(`appointments_${userId}`, JSON.stringify(appointments));
            return true;
        }

        return false;
    },

    // Get upcoming appointments (not cancelled)
    getUpcomingAppointments: function(userId) {
        const appointments = this.getAppointments(userId);
        return appointments.filter(a => a.status === 'confirmed');
    }
};

// Function to load user appointments
function loadUserAppointments(uid) {
    const appointmentsContainer = document.getElementById('user-appointments');
    if (!appointmentsContainer) return;

    // Show loading state
    appointmentsContainer.innerHTML = '<p>Loading appointments...</p>';

    // Get appointments from local storage
    setTimeout(() => {
        const appointments = AppointmentManager.getUpcomingAppointments(uid);

        if (appointments.length === 0) {
            // No appointments
            appointmentsContainer.innerHTML = `
                <div class="no-appointments">
                    <i class="fas fa-calendar-plus"></i>
                    <h3>No Upcoming Appointments</h3>
                    <p>You don't have any scheduled appointments at the moment.</p>
                    <a href="#book-appointment-content" class="book-now-link btn-primary"><i class="fas fa-plus-circle"></i> Book an Appointment</a>
                </div>
            `;

            // Re-attach event listener to the new book-now link
            const bookNowLink = appointmentsContainer.querySelector('.book-now-link');
            if (bookNowLink) {
                bookNowLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    const navLink = document.querySelector('.nav-links li[data-page="book-appointment-content"]');
                    if (navLink) {
                        navLink.click();
                    }
                });
            }
        } else {
            // Display appointments
            let appointmentsHTML = '';

            appointments.forEach(appointment => {
                // Parse date for formatting
                const appointmentDate = new Date(appointment.date);
                const month = appointmentDate.toLocaleString('default', { month: 'short' });
                const day = appointmentDate.getDate();

                appointmentsHTML += `
                    <div class="appointment-item" data-id="${appointment.id}">
                        <div class="appointment-date">
                            <span class="month">${month}</span>
                            <span class="day">${day}</span>
                        </div>
                        <div class="appointment-details">
                            <h3>Appointment with ${appointment.doctorName}</h3>
                            <p><i class="fas fa-clock"></i> ${appointment.time}</p>
                            <p><i class="fas fa-map-marker-alt"></i> ${appointment.location}</p>
                        </div>
                        <div class="appointment-actions">
                            <button class="btn-outline reschedule-btn">Reschedule</button>
                            <button class="btn-danger cancel-btn">Cancel</button>
                        </div>
                    </div>
                `;
            });

            appointmentsContainer.innerHTML = appointmentsHTML;

            // Add event listeners to cancel buttons
            const cancelButtons = appointmentsContainer.querySelectorAll('.cancel-btn');
            cancelButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const appointmentItem = this.closest('.appointment-item');
                    const appointmentId = appointmentItem.getAttribute('data-id');

                    if (confirm('Are you sure you want to cancel this appointment?')) {
                        if (AppointmentManager.cancelAppointment(uid, appointmentId)) {
                            // Reload appointments
                            loadUserAppointments(uid);
                            showAlert('Appointment cancelled successfully', 'success');
                        } else {
                            showAlert('Error cancelling appointment', 'error');
                        }
                    }
                });
            });

            // Add event listeners to reschedule buttons
            const rescheduleButtons = appointmentsContainer.querySelectorAll('.reschedule-btn');
            rescheduleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const appointmentItem = this.closest('.appointment-item');
                    const doctorName = appointmentItem.querySelector('.appointment-details h3').textContent.replace('Appointment with ', '');

                    // Navigate to booking page
                    const navLink = document.querySelector('.nav-links li[data-page="book-appointment-content"]');
                    if (navLink) {
                        navLink.click();

                        // Pre-fill the specialty field based on doctor name
                        setTimeout(() => {
                            const specialtySelect = document.getElementById('specialty');
                            if (specialtySelect) {
                                // Try to match doctor name to specialty
                                if (doctorName.includes('John Smith')) {
                                    specialtySelect.value = 'general-physician';
                                } else if (doctorName.includes('Sarah Johnson')) {
                                    specialtySelect.value = 'cardiologist';
                                } else if (doctorName.includes('Michael Chen')) {
                                    specialtySelect.value = 'pediatrician';
                                }
                            }

                            showAlert('Please select a new time slot for your appointment', 'info');
                        }, 500);
                    }
                });
            });
        }
    }, 1000);
}

