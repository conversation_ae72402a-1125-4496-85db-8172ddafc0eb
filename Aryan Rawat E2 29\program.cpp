#include <iostream>
#include <queue>
#include <vector>
using namespace std;

// Node structure for the min heap
struct MinHeapNode {
    char data;
    unsigned freq;
    MinHeapNode *left, *right;

    MinHeapNode(char data, unsigned freq) {
        this->data = data;
        this->freq = freq;
        left = right = nullptr;
    }
};

// Comparison structure for priority queue
struct compare {
    bool operator()(MinHeapNode* left, MinHeapNode* right) {
        return left->freq > right->freq;
    }
};

// Print Huffman Codes
void printCodes(MinHeapNode* root, string str) {
    if (!root) return;
    if (root->data != '$')
        cout << root->data << " : " << str << endl;
    printCodes(root->left, str + "0");
    printCodes(root->right, str + "1");
}

// Build Huffman Tree and print codes
void HuffmanCodes(char data[], int freq[], int size) {
    priority_queue<MinHeapNode*, vector<MinHeapNode*>, compare> minHeap;

    // Create a min heap with the given characters and their frequencies
    for (int i = 0; i < size; ++i)
        minHeap.push(new MinHeapNode(data[i], freq[i]));

    // Iterate until the heap has only one node
    while (minHeap.size() > 1) {
        // Extract the two nodes with the lowest frequency
        MinHeapNode* left = minHeap.top(); minHeap.pop();
        MinHeapNode* right = minHeap.top(); minHeap.pop();

        // Create a new internal node with frequency equal to the sum of the two nodes
        MinHeapNode* top = new MinHeapNode('$', left->freq + right->freq);
        top->left = left;
        top->right = right;

        // Add the new node back to the min heap
        minHeap.push(top);
    }

    // Print the Huffman codes from the root of the Huffman tree
    printCodes(minHeap.top(), "");
}

// Main function
int main() {
    char arr[] = { 'a', 'b', 'c', 'd', 'e', 'f' };
    int freq[] = { 5, 9, 12, 13, 16, 45 };
    int size = sizeof(arr) / sizeof(arr[0]);

    cout << "Huffman Codes:\n";
    HuffmanCodes(arr, freq, size);

    return 0;
}
    