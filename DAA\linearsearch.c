#include <stdio.h>
#include <stdlib.h>
int IndexofLinearSearch(int arr[] , int size , int key)
{
     for(int i = 0; i < size; i++)
     {
          if(arr[i] == key)
          return i; //returns the index of the key
     }
     return -1;
}
int main()
{

      int arr[] = {54,89,10,12,78,36,96,12,45}; //unsorted array
     int size = sizeof(arr)/sizeof(int);
     int key = 5; // Enter anything you want to find
     printf("The key is found at index %d." , IndexofLinearSearch(arr,size,key));
     return 0;
}