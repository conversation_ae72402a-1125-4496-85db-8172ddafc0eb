#include <stdio.h> 
#include <stdlib.h> 
 
void swap(int* a, int* b) { 
    int t = *a; 
    *a = *b; 
    *b = t; 
} 
 
int partition(int arr[], int l, int h) { 
    int pivot = arr[l]; 
    int i = l; 
    int j = h; 
    while (i < j) { 
        while (arr[i] <= pivot && i <= h - 1) { 
            i++; 
        } 
        while (arr[j] > pivot && j >= l + 1) { 
            j--; 
        } 
        if (i < j) { 
            swap(&arr[i], &arr[j]); 
        } 
    } 
    swap(&arr[l], &arr[j]); 
    return j; 
} 
 
int quickselect(int arr[], int low, int high, int k) { 
    if (low <= high) { 
        int pi = partition(arr, low, high); 
        if (pi == k - 1) 
            return arr[pi]; 
        else if (pi > k - 1) 
            return quickselect(arr, low, pi - 1, k); 
        else 
            return quickselect(arr, pi + 1, high, k); 
    } 
    return -1;  
} 
 
int main() { 
    int T; 
    int arr[100]; 
    printf("Enter the number of test cases :"); 
    scanf("%d", &T); 
    for (int t = 0; t < T; t++) { 
        int n; 
        printf("Enter the number of elements in the array :"); 
        scanf("%d", &n); 
        printf("Enter the elements :"); 
        for (int i = 0; i < n; i++) { 
            scanf("%d", &arr[i]); 
        } 
        int k; 
        printf("Enter the k'th term int the array:"); 
        scanf("%d", &k); 
        if (k <= 0 || k > n) { 
            printf("not present\n"); 
        } else { 
            int result = quickselect(arr, 0, n - 1, k); 
            printf("%d\n", result); 
        } 
    } 
    return 0; 
} 
