<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthCare Portal</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Maps API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBoXWfyoAZxOQN_5qI6pdDZrnI0usmVqSc&libraries=places" defer></script>
    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js";
        import { getAuth } from "https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js";
        import { getFirestore } from "https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js";
        import { getStorage } from "https://www.gstatic.com/firebasejs/9.22.0/firebase-storage.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/9.22.0/firebase-analytics.js";

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBRDjU2ralmIe64sBXJQDE7mfJ3isuEKK0",
            authDomain: "healthcare-website-33881.firebaseapp.com",
            projectId: "healthcare-website-33881",
            storageBucket: "healthcare-website-33881.firebasestorage.app",
            messagingSenderId: "283412304605",
            appId: "1:283412304605:web:895712be148e5ce5fd0134",
            measurementId: "G-SQ27H6WW6L"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const storage = getStorage(app);
        const analytics = getAnalytics(app);

        // Make Firebase services available globally
        window.firebaseApp = app;
        window.firebaseAuth = auth;
        window.firebaseDb = db;
        window.firebaseStorage = storage;
        window.firebaseAnalytics = analytics;
    </script>
</head>
<body>
    <div id="app">
        <!-- Login Page -->
        <div id="login-page" class="page active">
            <div class="container">
                <div class="login-container">
                    <div class="logo">
                        <i class="fas fa-hospital-user"></i>
                        <h1>HealthCare Portal</h1>
                    </div>
                    <div class="auth-tabs">
                        <div class="tab-header">
                            <div class="tab active" data-tab="login">Sign In</div>
                            <div class="tab" data-tab="signup">Sign Up</div>
                        </div>
                        <div class="tab-content">
                            <!-- Login Form -->
                            <div id="login-tab" class="tab-pane active">
                                <form id="login-form">
                                    <div class="form-group">
                                        <label for="login-email">Email</label>
                                        <input type="email" id="login-email" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="login-password">Password</label>
                                        <input type="password" id="login-password" required>
                                    </div>
                                    <div class="form-group remember-me">
                                        <input type="checkbox" id="remember-me">
                                        <label for="remember-me">Remember me</label>
                                    </div>
                                    <button type="submit" class="btn-primary">Login</button>
                                </form>
                                <div class="forgot-password">
                                    <a href="#" id="forgot-password-link">Forgot Password?</a>
                                </div>
                            </div>

                            <!-- Sign Up Form -->
                            <div id="signup-tab" class="tab-pane">
                                <form id="signup-form">
                                    <div class="form-group">
                                        <label for="signup-name">Full Name</label>
                                        <input type="text" id="signup-name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="signup-email">Email</label>
                                        <input type="email" id="signup-email" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="signup-password">Password</label>
                                        <input type="password" id="signup-password" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="signup-confirm-password">Confirm Password</label>
                                        <input type="password" id="signup-confirm-password" required>
                                    </div>
                                    <button type="submit" class="btn-primary">Create Account</button>
                                </form>
                                <div class="terms">
                                    <p>By signing up, you agree to our <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboard-page" class="page">
            <nav class="sidebar">
                <div class="logo">
                    <i class="fas fa-hospital-user"></i>
                    <h2>HealthCare Portal</h2>
                </div>
                <ul class="nav-links">
                    <li class="active" data-page="dashboard-content">
                        <a href="#dashboard-content"><i class="fas fa-home"></i> Dashboard</a>
                    </li>
                    <li data-page="book-appointment-content">
                        <a href="#book-appointment-content"><i class="fas fa-calendar-check"></i> Book Appointment</a>
                    </li>
                    <li data-page="hospitals-content">
                        <a href="#hospitals-content"><i class="fas fa-hospital"></i> Hospitals Near Me</a>
                    </li>
                    <li data-page="pharmacy-content">
                        <a href="#pharmacy-content"><i class="fas fa-prescription-bottle-alt"></i> Pharmacy Locator</a>
                    </li>
                    <li data-page="chatbot-content">
                        <a href="#chatbot-content"><i class="fas fa-robot"></i> Symptom Checker</a>

                    </li>
                    <li data-page="blood-availability-content">
                        <a href="#blood-availability-content"><i class="fas fa-tint"></i> Blood Availability</a>
                    </li>
                    <li data-page="calculators-content">
                        <a href="#calculators-content"><i class="fas fa-calculator"></i> Health Calculators</a>
                    </li>
                    <li data-page="profile-content">
                        <a href="#profile-content"><i class="fas fa-user"></i> Profile</a>
                    </li>
                </ul>
                <div class="logout">
                    <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
            </nav>

            <main class="content">
                <header>
                    <div class="header-content">
                        <h1>Welcome, <span id="user-name">Patient</span></h1>
                        <div class="user-info">
                            <span id="current-date"></span>
                            <div class="user-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Dashboard Content -->
                <div id="dashboard-content" class="page-content active">
                    <div class="dashboard-cards">
                        <div class="card">
                            <div class="card-icon"><i class="fas fa-calendar-check"></i></div>
                            <div class="card-info">
                                <h3>Book Appointment</h3>
                                <p>Find and book appointments with doctors</p>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-icon"><i class="fas fa-hospital"></i></div>
                            <div class="card-info">
                                <h3>Hospitals Near Me</h3>
                                <p>Find hospitals in your area</p>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-icon"><i class="fas fa-prescription-bottle-alt"></i></div>
                            <div class="card-info">
                                <h3>Pharmacy Locator</h3>
                                <p>Find pharmacies and check medicine availability</p>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-icon"><i class="fas fa-robot"></i></div>
                            <div class="card-info">
                                <h3>Symptom Checker</h3>
                                <p>Check your symptoms with our AI assistant</p>
                            </div>
                        </div>
                        <div class="card" id="blood-availability-card">
                            <div class="card-icon"><i class="fas fa-tint"></i></div>
                            <div class="card-info">
                                <h3>Blood Availability</h3>
                                <p>Check available blood types in Dehradun blood banks</p>
                            </div>
                        </div>
                    </div>


                    <div class="upcoming-appointments">
                        <h2>Your Upcoming Appointments</h2>
                        <div id="user-appointments" class="appointment-list">
                            <!-- Appointments will be loaded dynamically -->
                            <div class="no-appointments">
                                <i class="fas fa-calendar-plus"></i>
                                <h3>No Upcoming Appointments</h3>
                                <p>You don't have any scheduled appointments at the moment.</p>
                                <a href="#book-appointment-content" class="book-now-link btn-primary"><i class="fas fa-plus-circle"></i> Book an Appointment</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Book Appointment Content -->
                <div id="book-appointment-content" class="page-content">
                    <h2>Book an Appointment</h2>
                    <div class="search-container">
                        <div class="search-filters">
                            <div class="form-group">
                                <label for="specialty">Specialty</label>
                                <select id="specialty">
                                    <option value="">All Specialties</option>
                                    <option value="general-physician">General Physician</option>
                                    <option value="cardiologist">Cardiologist</option>
                                    <option value="dermatologist">Dermatologist</option>
                                    <option value="orthopedic">Orthopedic</option>
                                    <option value="pediatrician">Pediatrician</option>
                                    <option value="gynecologist">Gynecologist</option>
                                    <option value="neurologist">Neurologist</option>
                                    <option value="psychiatrist">Psychiatrist</option>
                                    <option value="dentist">Dentist</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="location">Location</label>
                                <input type="text" id="location" placeholder="Enter location">
                                <button id="use-my-location" class="btn-outline"><i class="fas fa-map-marker-alt"></i> Use my location</button>
                            </div>
                            <div class="form-group">
                                <label for="date">Date</label>
                                <input type="date" id="appointment-date">
                            </div>
                            <button id="search-doctors" class="btn-primary">Search</button>
                        </div>
                    </div>

                    <div id="doctors-list" class="doctors-container">
                        <!-- Doctors will be loaded here -->
                    </div>
                </div>

                

                <!-- Hospitals Near Me Content -->
                <div id="hospitals-content" class="page-content">
                    <h2>Hospitals Near Me</h2>
                    <div class="map-container">
                        <div class="map-filters">
                            <div class="form-group">
                                <label for="hospital-type">Hospital Type</label>
                                <select id="hospital-type">
                                    <option value="">All Types</option>
                                    <option value="general">General</option>
                                    <option value="emergency">Emergency</option>
                                    <option value="maternity">Maternity</option>
                                    <option value="pediatric">Pediatric</option>
                                    <option value="psychiatric">Psychiatric</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="hospital-location">Location</label>
                                <input type="text" id="hospital-location" placeholder="Enter location">
                                <button id="hospital-use-my-location" class="btn-outline"><i class="fas fa-map-marker-alt"></i> Use my location</button>
                            </div>
                            <button id="search-hospitals" class="btn-primary">Search</button>
                        </div>
                        <div id="hospitals-map" class="map"></div>
                    </div>

                    <div id="hospitals-list" class="hospitals-container">
                        <!-- Hospitals will be loaded here -->
                    </div>
                </div>

                <!-- Pharmacy Locator Content -->
                <div id="pharmacy-content" class="page-content">
                    <h2>Pharmacy Locator</h2>
                    <div class="map-container">
                        <div class="map-filters">
                            <div class="form-group">
                                <label for="pharmacy-location">Location</label>
                                <input type="text" id="pharmacy-location" placeholder="Enter location">
                                <button id="pharmacy-use-my-location" class="btn-outline"><i class="fas fa-map-marker-alt"></i> Use my location</button>
                            </div>
                            <div class="form-group">
                                <label for="medicine-search">Medicine Search (Optional)</label>
                                <input type="text" id="medicine-search" placeholder="Search for medicine availability">
                            </div>
                            <button id="search-pharmacies" class="btn-primary">Search</button>
                        </div>
                        <div id="pharmacies-map" class="map"></div>
                    </div>

                    <div id="pharmacies-list" class="pharmacies-container">
                        <!-- Pharmacies will be loaded here -->
                    </div>
                </div>

                <!-- Symptom Checker Content -->
                <div id="chatbot-content" class="page-content">
                    <h2>AI Symptom Checker</h2>
                    <div class="chatbot-container">
                        <div class="chat-messages" id="chat-messages">
                            <div class="message bot">
                                <div class="message-content">
                                    <p>Hello! I'm your health assistant. I can help you check your symptoms and provide basic health advice. What symptoms are you experiencing today?</p>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input">
                            <input type="text" id="user-message" placeholder="Type your symptoms here...">
                            <button id="send-message" class="btn-primary"><i class="fas fa-paper-plane"></i></button>
                        </div>
                    </div>
                </div>
                <!-- Blood Availability Content -->
                <div class="page-content" id="blood-availability-content">
                    <h2>Check Blood Availability</h2>
                    <div class="search-container">
                        <div class="search-filters">
                            <div class="form-group">
                                <label for="blood-group-select">Blood Group</label>
                                <select id="blood-group-select">
                                    <option value="">All Blood Groups</option>
                                    <option value="A+">A+</option>
                                    <option value="A-">A-</option>
                                    <option value="B+">B+</option>
                                    <option value="B-">B-</option>
                                    <option value="O+">O+</option>
                                    <option value="O-">O-</option>
                                    <option value="AB+">AB+</option>
                                    <option value="AB-">AB-</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="blood-location-input">Location</label>
                                <input type="text" id="blood-location-input" placeholder="Enter location (e.g., Dehradun)">
                                <button class="btn-outline" id="use-blood-location-btn"><i class="fas fa-map-marker-alt"></i> Use My Current Location</button>
                            </div>
                            <button class="btn-primary" id="search-blood-btn">Search Blood Banks</button>
                        </div>
                        <div id="blood-results" class="results-container">
                            <!-- Blood bank results will be displayed here -->
                        </div>
                    </div>
                </div>



                <!-- Health Calculators Content -->
                <div id="calculators-content" class="page-content">
                    <h2>Health Calculators</h2>
                    <div class="calculators-container">
                        <div class="calculator-tabs">
                            <div class="tab-header">
                                <div class="tab active" data-calc="bmi">BMI Calculator</div>
                                <div class="tab" data-calc="calorie">Calorie Intake Calculator</div>
                            </div>

                            <div class="calculator-content">
                                <!-- BMI Calculator -->
                                <div id="bmi-calculator" class="calculator active">
                                    <h3>BMI Calculator</h3>
                                    <div class="calculator-form">
                                        <div class="form-group">
                                            <label for="height">Height (cm)</label>
                                            <input type="number" id="height" placeholder="Enter height in cm">
                                        </div>
                                        <div class="form-group">
                                            <label for="weight">Weight (kg)</label>
                                            <input type="number" id="weight" placeholder="Enter weight in kg">
                                        </div>
                                        <button id="calculate-bmi" class="btn-primary">Calculate BMI</button>
                                    </div>
                                    <div id="bmi-result" class="calculator-result"></div>
                                </div>

                                <!-- Calorie Intake Calculator -->
                                <div id="calorie-calculator" class="calculator">
                                    <h3>Indian Diet Calorie Calculator</h3>
                                    <div class="calculator-form">
                                        <div class="form-group">
                                            <label>Select Food Items</label>
                                            <div class="food-items">
                                                <div class="food-item">
                                                    <input type="checkbox" id="roti" class="food-checkbox">
                                                    <label for="roti">Roti</label>
                                                    <input type="number" id="roti-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                                <div class="food-item">
                                                    <input type="checkbox" id="rice" class="food-checkbox">
                                                    <label for="rice">Rice (1 cup)</label>
                                                    <input type="number" id="rice-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                                <div class="food-item">
                                                    <input type="checkbox" id="dal" class="food-checkbox">
                                                    <label for="dal">Dal (1 cup)</label>
                                                    <input type="number" id="dal-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                                <div class="food-item">
                                                    <input type="checkbox" id="sabzi" class="food-checkbox">
                                                    <label for="sabzi">Sabzi (1 cup)</label>
                                                    <input type="number" id="sabzi-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                                <div class="food-item">
                                                    <input type="checkbox" id="paneer" class="food-checkbox">
                                                    <label for="paneer">Paneer Dish (1 cup)</label>
                                                    <input type="number" id="paneer-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                                <div class="food-item">
                                                    <input type="checkbox" id="chicken" class="food-checkbox">
                                                    <label for="chicken">Chicken Curry (1 cup)</label>
                                                    <input type="number" id="chicken-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                                <div class="food-item">
                                                    <input type="checkbox" id="dosa" class="food-checkbox">
                                                    <label for="dosa">Dosa</label>
                                                    <input type="number" id="dosa-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                                <div class="food-item">
                                                    <input type="checkbox" id="idli" class="food-checkbox">
                                                    <label for="idli">Idli</label>
                                                    <input type="number" id="idli-qty" class="food-quantity" value="1" min="1">
                                                </div>
                                            </div>
                                        </div>
                                        <button id="calculate-calories" class="btn-primary">Calculate Calories</button>
                                    </div>
                                    <div id="calorie-result" class="calculator-result"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Content -->
                <div id="profile-content" class="page-content">
                    <h2>Profile</h2>
                    <div class="profile-container">
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="profile-info">
                                <h3 id="profile-name">John Doe</h3>
                                <p>User ID: <span id="user-id">U12345678</span></p>
                            </div>
                        </div>
                        <div class="profile-details">
                            <div class="profile-section">
                                <h3>Personal Information</h3>
                                <div class="profile-field">
                                    <label>Full Name</label>
                                    <input type="text" id="profile-full-name" value="John Doe" disabled>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                                <div class="profile-field">
                                    <label>Date of Birth</label>
                                    <input type="date" id="profile-dob" disabled>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                                <div class="profile-field">
                                    <label>Gender</label>
                                    <select id="profile-gender" disabled>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                        <option value="other">Other</option>
                                    </select>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                            </div>
                            <div class="profile-section">
                                <h3>Contact Information</h3>
                                <div class="profile-field">
                                    <label>Email</label>
                                    <input type="email" id="profile-email" value="<EMAIL>" disabled>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                                <div class="profile-field">
                                    <label>Phone</label>
                                    <input type="tel" id="profile-phone" disabled>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                                <div class="profile-field">
                                    <label>Address</label>
                                    <textarea id="profile-address" disabled></textarea>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                            </div>
                            <div class="profile-section">
                                <h3>Emergency Contact</h3>
                                <div class="profile-field">
                                    <label>Name</label>
                                    <input type="text" id="emergency-name" disabled>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                                <div class="profile-field">
                                    <label>Relationship</label>
                                    <input type="text" id="emergency-relationship" disabled>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                                <div class="profile-field">
                                    <label>Phone</label>
                                    <input type="tel" id="emergency-phone" disabled>
                                    <button class="edit-btn"><i class="fas fa-edit"></i></button>
                                </div>
                            </div>
                            <div class="profile-section">
                                <h3>Account Security</h3>
                                <div class="profile-field">
                                    <button id="change-password-btn" class="btn-secondary">Change Password</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Main JavaScript -->
    <script src="script.js"></script>
</body>
</html>
