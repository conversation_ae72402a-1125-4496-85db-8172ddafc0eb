#include <stdio.h>
#include <stdlib.h>
#include <math.h>
int min(int a,int b)
{
    if (a<b)
    return a;
    else
    return b;
}
int IndexofJumpSearch(int arr[], int size, int key)
{
    int step = sqrt(size); // Calculate the block size to jump
    int prev = 0;

    // Finding the block where the element is present
    while (arr[min(step, size) - 1] < key)  
    {
        prev = step;
        step += sqrt(size);
        if (prev >= size)
            return -1; // If we've reached the end of the array
    }

    // Doing a linear search for key in the block
    while (arr[prev] < key)
    {
        prev++;
        if (prev == min(step, size))
            return -1; // If we've reached the end of the block
    }

    // If the element is found
    if (arr[prev] == key)
        return prev;

    return -1; // If the element is not found
}
int main()
{
     int arr[] = {5,10,15,20,25,30,35,40,45,50}; //sorted array
    int size = sizeof(arr)/sizeof(int);
     int key = 45; // Enter anything you want to find
     printf("The key is found at index %d." , IndexofJumpSearch(arr,size,key));
     return 0;
}